#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile '.\requirements.in'
#
aiohappyeyeballs==2.4.4
    # via aiohttp
aiohttp==3.11.11
    # via
    #   langchain
    #   langchain-community
aiosignal==1.3.2
    # via aiohttp
alembic==1.14.0
    # via flask-migrate
annotated-types==0.7.0
    # via pydantic
anyio==4.8.0
    # via
    #   httpx
    #   openai
apispec[yaml]==6.8.1
    # via
    #   -r .\requirements.in
    #   apispec-webframeworks
apispec-webframeworks==1.2.0
    # via -r .\requirements.in
art==6.4
    # via -r .\requirements.in
attrs==24.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
azure-ai-documentintelligence==1.0.0
    # via -r .\requirements.in
azure-ai-ml==1.23.1
    # via -r .\requirements.in
azure-common==1.1.28
    # via
    #   azure-ai-ml
    #   azure-search-documents
azure-core==1.32.0
    # via
    #   -r .\requirements.in
    #   azure-ai-documentintelligence
    #   azure-ai-ml
    #   azure-identity
    #   azure-mgmt-core
    #   azure-search-documents
    #   azure-storage-blob
    #   azure-storage-file-datalake
    #   azure-storage-file-share
    #   msrest
    #   opencensus-ext-azure
azure-identity==1.19.0
    # via
    #   -r .\requirements.in
    #   opencensus-ext-azure
azure-mgmt-core==1.5.0
    # via azure-ai-ml
azure-search-documents==11.5.2
    # via -r .\requirements.in
azure-storage-blob==12.24.0
    # via
    #   -r .\requirements.in
    #   azure-ai-ml
    #   azure-storage-file-datalake
azure-storage-file-datalake==12.18.0
    # via azure-ai-ml
azure-storage-file-share==12.20.0
    # via azure-ai-ml
bidict==0.23.1
    # via python-socketio
blinker==1.9.0
    # via flask
build==1.2.2.post1
    # via pip-tools
cachelib==0.13.0
    # via flask-session
cachetools==5.5.0
    # via google-auth
certifi==2024.12.14
    # via
    #   httpcore
    #   httpx
    #   msrest
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.1
    # via
    #   pdfminer-six
    #   reportlab
    #   requests
click==8.1.8
    # via
    #   click-option-group
    #   flask
    #   pip-tools
    #   python-semantic-release
click-option-group==0.5.7
    # via python-semantic-release
colorama==0.4.6
    # via
    #   azure-ai-ml
    #   build
    #   click
    #   tqdm
concurrent-log-handler==0.9.25
    # via -r .\requirements.in
cryptography==44.0.0
    # via
    #   azure-identity
    #   azure-storage-blob
    #   azure-storage-file-share
    #   msal
    #   oracledb
    #   pdfminer-six
    #   pyjwt
dataclasses-json==0.6.7
    # via langchain-community
deprecated==1.2.18
    # via python-semantic-release
distro==1.9.0
    # via openai
docx2pdf==0.1.8
    # via -r .\requirements.in
dotty-dict==1.3.1
    # via python-semantic-release
et-xmlfile==2.0.0
    # via openpyxl
fire==0.7.0
    # via pdf2docx
flask==3.1.0
    # via
    #   -r .\requirements.in
    #   flask-login
    #   flask-migrate
    #   flask-session
    #   flask-socketio
    #   flask-sqlalchemy
flask-login==0.6.3
    # via -r .\requirements.in
flask-migrate==4.1.0
    # via -r .\requirements.in
flask-session==0.8.0
    # via -r .\requirements.in
flask-socketio==5.5.1
    # via -r .\requirements.in
flask-sqlalchemy==3.1.1
    # via
    #   -r .\requirements.in
    #   flask-migrate
fonttools==4.59.0
    # via pdf2docx
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via python-semantic-release
google-api-core==2.24.0
    # via opencensus
google-auth==2.37.0
    # via google-api-core
googleapis-common-protos==1.66.0
    # via google-api-core
greenlet==3.1.1
    # via sqlalchemy
h11==0.14.0
    # via
    #   httpcore
    #   wsproto
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via
    #   -r .\requirements.in
    #   langsmith
    #   openai
httpx-sse==0.4.0
    # via langchain-community
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
importlib-resources==6.5.2
    # via python-semantic-release
isodate==0.6.1
    # via
    #   azure-ai-documentintelligence
    #   azure-ai-ml
    #   azure-search-documents
    #   azure-storage-blob
    #   azure-storage-file-datalake
    #   azure-storage-file-share
    #   msrest
itsdangerous==2.2.0
    # via flask
jinja2==3.1.5
    # via
    #   flask
    #   python-semantic-release
jiter==0.8.2
    # via openai
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.23.0
    # via azure-ai-ml
jsonschema-specifications==2024.10.1
    # via jsonschema
langchain==0.3.14
    # via
    #   -r .\requirements.in
    #   langchain-community
langchain-community==0.3.14
    # via -r .\requirements.in
langchain-core==0.3.30
    # via
    #   -r .\requirements.in
    #   langchain
    #   langchain-community
    #   langchain-openai
    #   langchain-text-splitters
langchain-openai==0.3.0
    # via -r .\requirements.in
langchain-text-splitters==0.3.5
    # via
    #   -r .\requirements.in
    #   langchain
langsmith==0.2.11
    # via
    #   langchain
    #   langchain-community
    #   langchain-core
lxml==5.3.0
    # via
    #   python-docx
    #   python-pptx
mako==1.3.8
    # via alembic
markdown==3.8
    # via -r .\requirements.in
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via
    #   jinja2
    #   mako
    #   werkzeug
marshmallow==3.25.1
    # via
    #   -r .\requirements.in
    #   azure-ai-ml
    #   dataclasses-json
mdurl==0.1.2
    # via markdown-it-py
msal==1.31.1
    # via
    #   azure-identity
    #   msal-extensions
msal-extensions==1.2.0
    # via azure-identity
msgspec==0.19.0
    # via flask-session
msrest==0.7.1
    # via azure-ai-ml
multidict==6.1.0
    # via
    #   aiohttp
    #   yarl
mypy-extensions==1.0.0
    # via typing-inspect
numpy==2.2.1
    # via
    #   -r .\requirements.in
    #   langchain
    #   langchain-community
    #   opencv-python-headless
    #   pandas
    #   pdf2docx
oauthlib==3.2.2
    # via requests-oauthlib
openai==1.59.8
    # via
    #   -r .\requirements.in
    #   langchain-openai
opencensus==0.11.4
    # via
    #   opencensus-ext-azure
    #   opencensus-ext-logging
opencensus-context==0.1.3
    # via opencensus
opencensus-ext-azure==1.1.14
    # via azure-ai-ml
opencensus-ext-logging==0.1.1
    # via azure-ai-ml
opencv-python-headless==4.12.0.88
    # via pdf2docx
openpyxl==3.1.5
    # via -r .\requirements.in
oracledb==2.5.1
    # via -r .\requirements.in
orjson==3.10.14
    # via langsmith
packaging==24.2
    # via
    #   apispec
    #   build
    #   langchain-core
    #   marshmallow
pandas==2.2.3
    # via -r .\requirements.in
pdf2docx==0.5.8
    # via -r .\requirements.in
pdfminer-six==20250506
    # via pdfplumber
pdfplumber==0.11.7
    # via -r .\requirements.in
pillow==11.3.0
    # via
    #   pdfplumber
    #   python-pptx
    #   reportlab
pip-system-certs==4.0
    # via -r .\requirements.in
pip-tools==7.4.1
    # via -r .\requirements.in
portalocker==2.10.1
    # via
    #   concurrent-log-handler
    #   msal-extensions
propcache==0.2.1
    # via
    #   aiohttp
    #   yarl
proto-plus==1.25.0
    # via google-api-core
protobuf==5.29.3
    # via
    #   google-api-core
    #   googleapis-common-protos
    #   proto-plus
psutil==6.1.1
    # via opencensus-ext-azure
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.1
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.10.5
    # via
    #   langchain
    #   langchain-core
    #   langsmith
    #   openai
    #   pydantic-settings
    #   python-semantic-release
pydantic-core==2.27.2
    # via pydantic
pydantic-settings==2.7.1
    # via langchain-community
pydash==8.0.5
    # via azure-ai-ml
pygments==2.19.2
    # via rich
pyjwt[crypto]==2.10.1
    # via
    #   azure-ai-ml
    #   msal
pymssql==2.3.2
    # via -r .\requirements.in
pymupdf==1.26.3
    # via
    #   -r .\requirements.in
    #   pdf2docx
pyodbc==5.2.0
    # via -r .\requirements.in
pypdf==5.9.0
    # via -r .\requirements.in
pypdf2==3.0.1
    # via -r .\requirements.in
pypdfium2==4.30.0
    # via pdfplumber
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
python-dateutil==2.9.0.post0
    # via
    #   pandas
    #   strictyaml
python-docx==1.2.0
    # via
    #   -r .\requirements.in
    #   pdf2docx
python-dotenv==1.0.1
    # via pydantic-settings
python-engineio==4.11.2
    # via python-socketio
python-gitlab==6.0.0
    # via python-semantic-release
python-pptx==1.0.2
    # via -r .\requirements.in
python-semantic-release==10.1.0
    # via -r .\requirements.in
python-socketio==5.12.1
    # via flask-socketio
pytz==2024.2
    # via pandas
pywin32==308
    # via
    #   docx2pdf
    #   portalocker
pyyaml==6.0.2
    # via
    #   apispec
    #   azure-ai-ml
    #   langchain
    #   langchain-community
    #   langchain-core
referencing==0.36.1
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via tiktoken
reportlab==4.4.3
    # via -r .\requirements.in
requests==2.32.3
    # via
    #   azure-core
    #   google-api-core
    #   langchain
    #   langchain-community
    #   langsmith
    #   msal
    #   msrest
    #   opencensus-ext-azure
    #   python-gitlab
    #   python-semantic-release
    #   requests-oauthlib
    #   requests-toolbelt
    #   tiktoken
requests-oauthlib==2.0.0
    # via msrest
requests-toolbelt==1.0.0
    # via
    #   langsmith
    #   python-gitlab
rich==14.0.0
    # via python-semantic-release
rpds-py==0.22.3
    # via
    #   jsonschema
    #   referencing
rsa==4.9
    # via google-auth
shellingham==1.5.4
    # via python-semantic-release
simple-websocket==1.1.0
    # via python-engineio
six==1.17.0
    # via
    #   azure-core
    #   isodate
    #   opencensus
    #   python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anyio
    #   openai
sqlalchemy==2.0.37
    # via
    #   alembic
    #   flask-sqlalchemy
    #   langchain
    #   langchain-community
sqlglot==26.19.0
    # via -r .\requirements.in
sqlparse==0.5.3
    # via -r .\requirements.in
strictyaml==1.7.3
    # via azure-ai-ml
tabulate==0.9.0
    # via -r .\requirements.in
tenacity==9.0.0
    # via
    #   -r .\requirements.in
    #   langchain
    #   langchain-community
    #   langchain-core
termcolor==3.1.0
    # via fire
tiktoken==0.8.0
    # via
    #   -r .\requirements.in
    #   langchain-openai
tomlkit==0.13.3
    # via python-semantic-release
tqdm==4.67.1
    # via
    #   -r .\requirements.in
    #   azure-ai-ml
    #   docx2pdf
    #   openai
truststore==0.10.0
    # via -r .\requirements.in
typing-extensions==4.12.2
    # via
    #   alembic
    #   anyio
    #   azure-ai-documentintelligence
    #   azure-ai-ml
    #   azure-core
    #   azure-identity
    #   azure-search-documents
    #   azure-storage-blob
    #   azure-storage-file-datalake
    #   azure-storage-file-share
    #   langchain-core
    #   openai
    #   pydantic
    #   pydantic-core
    #   pydash
    #   python-docx
    #   python-pptx
    #   referencing
    #   sqlalchemy
    #   typing-inspect
typing-inspect==0.9.0
    # via dataclasses-json
tzdata==2024.2
    # via pandas
urllib3==2.3.0
    # via requests
werkzeug==3.1.3
    # via
    #   -r .\requirements.in
    #   flask
    #   flask-login
wfastcgi==3.0.0
    # via -r .\requirements.in
wheel==0.45.1
    # via pip-tools
wrapt==1.17.2
    # via
    #   deprecated
    #   pip-system-certs
wsproto==1.2.0
    # via simple-websocket
xlsxwriter==3.2.0
    # via
    #   -r .\requirements.in
    #   python-pptx
yarl==1.18.3
    # via aiohttp

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
