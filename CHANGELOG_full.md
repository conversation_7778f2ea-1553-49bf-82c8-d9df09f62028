# CHANGELOG



## v1.1.0 (2025-07-11)

### Features

* feat: added waiting message when ai services are starting

* feat: improved boot time

### Unknown

* added the PRIDE_PDB_LOGISTIC_IMPROVED and PRIDE_PDB_MAINDATA_IMPROVED tables to Call Center Bot schema and added the logic that changes the product code if it starts with a letter

* Changelog beautify


## v1.0.3 (2025-07-10)

### Bug fixes

* fix: show sql button fixed

### Refactoring

* refactor: removed unused old auth code
feat: new users admin gui
fix: loglevel from config file

### Unknown

* added SEO Bot in insecure_chat

* Updated changelog template

* improved log files\nloglevel info for production

* Changed choices structure for agent choice

* beautify code

* Added promtp details for column ACTIVITY COUNTRY in SERVICE_JOBS table

* fix the prompt to better identify implosion questions

* fix for agent intent detection, added 'Go to Database' button in the document selection dialog

* Removed unused sqlite database


## v1.0.2 (2025-07-04)

### Bug fixes

* fix: Now the user knows the environment
- now the bot uses the final index for it-documents-pride
- little change on the setup script to set only the environment vars
without altering the venv

### Documentation

* docs: edit changelog


## v1.0.1 (2025-07-03)

### Bug fixes

* fix: returned correct document type in the response of the rag_document tool

* fix: Now user can save personal settings

### Documentation

* docs: improved deploy documentation

### Refactoring

* refactor: changed document type selection structure from string array to dict array

### Unknown

* Changelog improvement

* Fixed a bug for the insecure chat endpoint

* added reset for the variable of the SQLErrors class

* added default options for installation related questions and deleted the old service jobs table

* Updated readme

* Popup with changelog information

* Popup with changelog information


## v1.0.0 (2025-06-24)

### Unknown

* fix error in getting function valued tables for implosion queries

* Automatic versioning config


## v0.8.1 (2025-06-24)

### Unknown

* Automatic versioning config


## v0.8.0 (2025-06-24)

### Unknown

* added the new SEO Bot

* fix service jobs schema

* hotfix procurement

* Frontend build

* minor fixes

* added the SERVICE_JOBS_COMPLETE_LIST table

* Added loading animation when 'download all' button is clicked

* Automatic versioning config

* Full debug log for requests

* New document raw logs

* New document raw logs

* SQL BOM implosion intent detection fix

* fixes to the intent detection for implosion queries

* fixed link render in some corner case where tables in the query are generated with the alias

* fixed some schema names

* change the file of the implosion table

* fixes to the implosion table name and for the correct execution of implosion query

* modified intent detection prompt, changed the example retrieval process and added new table in the Call Center Bot schema

* Added mechanism to detect implosion queries and extract code and item/spare from the user question

* Modified PDF message

* procurement bot

* procurement bot

* Removed unused config files\nFix DB migration script

* Fix on filtering document type when searching document list

* added formatting options for rag and rag document answers in insecure_chat, added source_file_details in the response structure and renamed source_files to source_file

* Change default behaviour for document type extraction

* time optimization for thr generation of the answer of the RAG agent

* create components for different parts of the Answer

* prompt adjustment

* modified sql error handling considering more complex queries

* code optimization

* added logic to check if the field of the generated query candidates are present in the referenced table handling error cases

* added photo doc type

* add config staging

* Rebuild update frontend

* Fix feedback button

* Updated readme file

* New maintenance status for Web and API

* fixed bug on rag document

* added error message when document is not found for a specific product code

* added support for service jobs table

* fix for class initialization

* Fix error on Text2Sql agent

* Minor fixes on typos

* fix on insecure_chat considering the changes in secure_chat

* Fix log level change

* Fix language id validation on API

* Added distinct to the document list query

* Fix missing document_number parameter in document API

* Fix missing document_number parameter in document API

* Fix EPROExcelLa

* Added document status and document date in response and document number and document status filters in request

* fix on rag document refactoring

* Fix API documentation and error messages

* Added latest document edition document list retrieval

* fix on rag refactoring

* Added BELSCODE column in COMMERCIAL_PRODUCTS table

* backend refactoring for improving speed of the bot

* minor refactror in metadata checking

* refactring between rag and rag_document agent

* Refactor API for auto documentation

* recompiled frontend

* Added column in BOM table schema

* refcator the generate function to generate both the answer and the context

* refactoring of the answer component in subcomponents

* Document number - product number misclassification fix, food laundry interaction removed, frontend issue fixed

* fixed loop in context setting when follow-up question uses a different agent

* recompiled frontend

* Improvement on document and document list API calls

* Modified the endpoint /documents for retrieving documents with language id instead of language description

* added new endpoint documentList

* disabled Excel button for rag and rag document answer

* fixed non blocking error of reading the stream when it was already consumed

* refactor code for the rag agent and the generator function

* increse time to sleep when yielding the reposnse in the frontend

* Added BOM_PRMS table in the scope of CALL_CENTER bot

* added immages to the response

* increase time to sleep when yielding the response in streaming

* Added configurations for JD-AI-lo_BOT

* minor fix to retriever

* added the new JD-AI-lo bot

* Revert "Fix for insecure chat endpoint that is searching for email in session that is not present"

This reverts commit b0477994468cf9741b41f2596168dbc67db0cf9c.

* Fix frontend

* Added new columns to the COMMERCIAL_PRODUCT_DOCUMENTS table, frontend bug fix

* Intent detection fine tuned for text to sql document questions

* Added new endpoint for retreiving documents

* Added new graphic for manual choice, fixed application how to bot

* Fixes to the contex data visualization

* Various fixes for AnalysisPanel visualization, for Clear Chat, for RAG response and for empty intent detection

* fixed data visualization in AnalysisPannel for streaming responses

* added streaming response feature

* fix on intent detection

* DGTCCBOT-66 DGTCCBOT-67 added second step interaction with frontend integration

* fix on insecure chat

* added intent detection also to insecure chat

* First step interaction frontend

* First step interaction + optimization code

* add answer type parameter

* added PWD support

* Implemented show sql and explanation filter

* Fix logs

* Fix web.config

* New config for STG

* New config for STG

* New config for STG

* Debug setup.ps1

* Fix

* debug on Setup.ps1

* debug on Setup.ps1

* debug on Setup.ps1

* Added column descriptions support in prompt for text to SQL

* Electrical Wiring Diagram search enabled

* Programming file search, bug fix on subsequent rag questions

* Add preliminary question for food or laundry, code refactoring

* Using pip-tools to manage dependencies

* fix on variable name insecure chat endpoint

* Fix for insecure chat endpoint that is searching for email in session that is not present

* Fix web.config

* Fix setup script

* Fix logs

* Fix logs

* libraries refactoring

* Merge branch feature/RAG_PriDE into develop

* fix on related spare parts schema

* Fix on Spare part catalogue

* bitbucket-pipelines.yml edited online with Bitbucket_test_pull

* bitbucket-pipelines.yml edited online with Bitbucket

* Support fo all type of document retrieval, improving in metadata extraction for document number, refactoring on retrieval logic

* Fix on answer when document number search is done

* Added support for Document Number Search. Fix on document edition search. Installation Instruction and Technical Bulletin/Information search supported.

* bitbucket-pipelines.yml edited online with Bitbucket jobs a 2

* Modifiche Mind Security to BB pipeline

* Added test chat endpoint with no auth

* Merge branch 'develop'

* Common language logic fixed

* Improvement in retrieving images, added support for Spare Part Catalogs

* Fix numrows fetchet by sql client

* Improvement in processing big files

* Fix multilingual processing

* Fix number images when extraction fails

* fix saving image

* Modifiche Mind Security

* bitbucket-pipelines.yml edited online with Bitbucket

* Commit Semgrep pipeline

* bitbucket-pipelines.yml edited online with Bitbucket

* bitbucket-pipelines.yml edited online with Bitbucket

* bitbucket-pipelines.yml edited online with Bitbucket

* bitbucket-pipelines.yml edited online with Bitbucket

* bitbucket-pipelines.yml edited online with Bitbucket

* bitbucket-pipelines.yml edited online with Bitbucket

* bitbucket-pipelines.yml edited online with Bitbucket

* bitbucket-pipelines.yml edited online with Bitbucket

* Fix on setup environment

* frontend build

* change name bot to EPROExcelLa

* stable version

* Uploading file with user ID, now the DatSync Bot can manage a file for each user

* Added search whole document handbook

* Added support for Handbook search

* replace commit

* improved UX without asking metadata

* Initial commit

* Added new table COMMERCIAL_PRODUCTS_HIERARCHY in scope of Call center bot

* Example uploaded and update

* Example uploaded (Sami examples)

* Cleaned installed base examples

* excel modified

* fix multilingual

* multilingual fix

* Added support for US document languages

* Fix on row limit visualized in the bot

* build frontend and fix on history for rag document agent

* Download files section in explanation showed only for rag agent

* Fix some bugs and added comment to the code

* Merge branch 'TranslateBot' into develop

* Merge hotfix/InstalledBaseColumns into develop

* Frontend build

* Download button fix

* download button fixed

* Downloading button problem

* Correction of some bug, and partial implementation of TaskSelector

* DataSync: implementation file downloading

* v0.1 stable, adding more features

* Multi translation of v0.1 working

* v0.0 translator bot e passaggio verso la versione v0.1 FlExcel

* Installed base columns for iso2 countries updated

* Fix and improvements:
- Better definition of rag tool
- Icons moved to the left side of the chatbox answer
- Fixed bug in metadata extraction
- Go to the documentation button behavior modified for new agent
- Managed blob not found error for images

* Implemented request for whole documentation

* Implementation search whole document

* Added Document not found excepetion for better clarifying the nature of the error, added search in the SMA folder

* Fix on managing facotry model with '-' character inside, improve on metadata extraction prompt

* DGTCCBOT-2 Implemented zoom in images provided by chat answers

* test pull request

* Modified Commercial_products table schema for complibot scope

* Fix key example file

* last fix for rag documentation

* pride processing document complete

* Refactor images and tables and chunking .pptx

* Added following capabilities to call center bot rag:
1. Automatic language detection from user query, english default if the
   document is not present
   2. Automaitc retrieve the last edition of the document
   3. Download the file used for the answer
   4. Implemented the search on factory model with clarification

* initial commit for rag pride chatbot

* - Added spare parts schema and examples
- Added Complibot examples for compliance

* Uploaded new compliance examples

* Fix LOB issue

* bitbucket-pipelines.yml edited online with Bitbucket

* bitbucket-pipelines.yml edited online with Bitbucket

* bitbucket-pipelines.yml edited online with Bitbucket

* bitbucket-pipelines.yml edited online with Bitbucket

* bitbucket-pipelines.yml edited online with Bitbucket

* Initial Bitbucket Pipelines configuration

* Added new table for document processing status for pride documentation

* Changed COMMERCIAL_PRODUCTS 'OWNER' column to 'COMPANY_OWNER'

* gitignore

* fix clob

* support for rag images

* Changes:
- Button hidden for bot that don't support intent clairification
- Migration from gpt-3.5 to gpt-4o-mini

* Major improvements:
- Modified examples for installed base and commercial products in order to include internal code and product number in questions
- Added button for intent clarification
- Added sales order view in scope of call center bot
- Improvement in rag answer by modifing prompt

* Added new BOM example for spare parts

* Call center bot and rag improvement
- Added new tables in scope of call center bot
- fixed 'product code' generation column error
- fixed 'upper case' error for SITE_CITY and SITE_COUNTRY for query generation
- Added 'citation' tab for rag
- Added explanation for rag answers

* Added intent detection, rag history, optimized query speeed

* added description function

* intent detection

* Edited installed base examples

With adding 'OR' clause in where condition with product number and internal code

* Added example for machine-pnc and installed base call center bot

* Fine-tune HOW-TO max chunks parameter

* Fix import version

* Update examples

* Add max chunk parameter definition

* added installed_base examples

* Improve logging

* Fix BOM examples

* Upload examples

* Fix preliminary query generation issue

* Fix history-related errors

Fix problem where error output messages were treated as SQL messages
Fix problem where only admins were able to use the history without errors
Fix problem where LLMs could return None (now they return an empty string)

* Add exception-handler wrapper in chat

Refactoring required as this code may render the technical debt unsolvable

* PROVISIONAL: Add stacktrace to Exception response

* PROVISIONAL: Add try-except to capture and return all Exceptions raised in history parsing

* Fix index error in text-to-sql conversation data parsing

* Enrich history parser logging

* Add authentication logging

* Fix config

* Revert RAG config

* Update config parameters

* Change local document source to be a custom developer setting

* indexer "bot-aware"

* Make the relevant schema function bot-aware

* DGTAI-73 Add conversation history

Current issue: bot does not generate "multiple-table-queries" if the conversation started with a "single-table-query"

* Fix merge issue

* DGTAI-73 "Generalise" conversation history (WIP)

Works for Text-to-SQL only
RAG message-compiling still to be implemented

* DGTAI-73 "Generalise" conversation history (WIP)

Works for Text-to-SQL only
RAG message-compiling still to be implemented

* DGTAI-73 Add rough history

* Added support for dynamic configuration

* Change example index

Now points to the Sandbox resource containing the examples with the new bot names.

* Secure DEL chat/ and DEL chat/chat-sessions calls

KNOWN ISSUE - Security check in DEL chat/ fails when user invokes it on the first message.

* Bot selector
Fix BotType enum

* Bot selector (wip)

* Quick fix error response problem

* Revert "Merge branch 'conversation_history'"

This reverts commit cfe9ac0cc6e994713615db54297d3ab069553837, reversing
changes made to b16acecc68d90a373378f44a9893a344c3888101.

* modified string values with enum for bot and agents

* Fix ITEM_COMPLIANCES examples

* Fix merge undetected errors

* DGTAI-73 Add rough history

* refactoring bot

* config fix

* refactored text-to-sql-tool

* DGTAI-134 DGTAI-139 Upload new examples and update COMMERCIAL_PRODUCTS field names

* DGTAI-88 Improve stop generation button

Now button actually stops execution

* DGTAI-130 Update prompt

Instruct to better follow the examples provided.

* DGTAI-130 DGTAI-133 Upload examples

Examples from INSTALLED_BASE and SERIAL_NUMBERS were ignored.

* WIP refactoring - removed all unnecessary indexer file

* WIP refactoring - updated reference to the storage in the storer class

* WIP Refactoring - document_intelligence config

* WIP refactoring - added document_intelligence credentials to RAG Config class

* WIP refactoring

changed reference from utils.core of the indexer to utils.core of the complibot

* WIP refactoring - indexer config

* Improve key.private parsing

* fixed debug flask

* edited rag prompt for retreiving tables from blob storage

* logic for storing non-splitted tables in blob storage

* added logging to pride indexer rag

* edited config and requirements for merging pride indexer and complibot code

* added config for debug rag and indexer

* Adding indexer folder to the complibot folder

* added column in order to diversify bot's examples

* Enrich DDL schemas

* Fix setup script for staging and dev

* Fix LangChain's tenacity dependency issue

* Fix thumb up/down button fix

* DGTAI-131

* DGTAI-129 Upload examples to show tables' fields

* Update schemas

* DGTAI-9 - Excel download
DGTAI-42 - User feedback
DGTAI-114 - Stop button

* Excel export
Thumbs Up/Down feedback

* Improve similarity search

* Update Azure resources

* DGTAI-125 Augment schema and add examples

* DGTAI-125 Augment field informations

* Update RAG embedding deployment

* Update ConversationSession saving

* Update requirements

* Update config

* Fix examples

* DGTAI-88 Fix stop function representation

* Add example reformulator config JSON

* Remove useless print

* Integrate RAG config with Txt2SQL config

* Improve example_uploader logging

* Fix examples syntax

* Fix database migration problem

* DGTAI-100 Convert all agreement examples to use AGREEMENT_VIEW

* Improve explanation prompt

* DGTAI-119 Add clarification function

* question history

* Integrate RAG in current

* Fix issues

Fix duplicate message issue
Fix logging verbosity
Fix schema representation to include all visible tables

* DGTAI-105 Update agreement examples

* DGTAI-115 Add "cancel execution" backend functionality

* DGTAI-105 Change complibot schema

* DGTAI-105 Upload first batch of changes

* Change embedders to singleton pattern

* Change history visibility

* Improve models logging

* DGTAI-41 Improve example retrieval pipeline

Add examples based on inquiry to preliminary model invocation
Add hybrid search to fetch examples

* DGTAI-70 Upload examples

* Update configuration for new Azure resources

* DGTAI-70 Update examples

* DGTAI-70 Upload more recursive BoM queries

* Update examples

* Update examples

* calback schema (http[s]) in config file

* Fix password encoding

* Fix password encoding

* Build new gui

* Access MSSQL via pymssql

* Access MSSQL via pymssql

* DGTAI-35

* DGTAI-35 (wip)

* Improve code readability

* Fix spelling mistake in prompt skeleton

* DGTAI-83 Show both explanation and sql (only when present)

* DGTAI-83 Implement dedicated return type for CompliBot's API's answers

* Fix "InquryEmbedder" typo

* Remove handlers from non-root loggers

* DGTAI-35 (wip)

* Limit fetched rows to max 10

* DGTAI-41 Update schema to include BOM DDL

* DGTAI-70 Upload BOM examples

* Fix logging issue

* Comment certifi-win32

* Update requirements

* Add threading logging and change gitignore

Add threading identifier to logging
Add vscode files to gitignore

* Improve debugging

* Improve code readability

* Update logging and introduce weak explainability

Add dedicated logger to each script
Add explaination of query to CompliBot.ask output
Add unused import (certifi_win32) to facilitate pipreqs requirements generation

* DGTAI-2 Fix performance after test results

* DGTAI-60 Mitigate product code ambiguity through specific example

* DGTAI-67 Increase internal buffer size

* DGTAI-35 (wip)

* Fix DEV proxy url

* DGTAI-60 Upload COMMERCIAL_PRODUCTS examples

* DGTAI-60 Update table schemas

Add COMMERCIAL_PRODUCT schema
Remove useless tokens

* Proxy configuration

* DGTAI-60 Reformulate first 9 Product examples

* Add constraint to preliminary model

* Enhance schema to be more informative

* Refine examples

* Change accuracy to confidence

* DGTAI-64 Add self-correction mode

* DGTAI-64 Update LLM parameters

* Update gitignore to ignore GUI log files

* DGTAI-64 Modify examples to better capture COUNTRY_OF_ORIGIN behaviour

* Minor fix

* Fix log folder

* DGTAI-63 Refine message return on GUI

* DGTAI-63 Add technical feedback when all queries fail

* Fix web.config

* Update routes

* Update requirements

* New log_folder parameter in config.ini

* DGTAI-64 Increase number of examples in prompt

* Upload JOIN examples to improve responses

* Fix error randomUUID() on http connection

* Catch TooManyStatementsException

* DGTAI-64 Add examples to aid JOINS

* DGTAI-64 Add information on the contents of some fields

* DGTAI-64 Tune the LLM and the example selection

* Add feedback on query executions fails

* Add support for log to file
Fix setup.ps1 to update requirements

* DGTAI-62 (wip)

* Fix setup script for server deployment

* DGTAI-62 (wip)

* DGTAI-62 (wip)

* Update Agreements-Items join examples to match glossary

* Update embeddings

* Update examples that join AGREEMENT_HEADER, AGREEMENT_ROW, and ITEM_MASTER

* Change preliminary model

* DGTAI-63 Add catch to IncapableException

* Fix CLI

* Update Supplier representation

* Add current date to AI context

* DGTAI-57 Upload Suppliers examples

* DGTAI-53 Add new examples for Items

* Refine already uploaded examples

* Include schema names in DDL statements

* Test bot performance

* Change examples to include schema name in table name

* Ignore test runs data

* Delete test runs data

* Preparing for deploy

* Refactor backend

* Uniform equality check within bot API

* Add personal configuration

* DGTAI-20 Add SUPPLIER schema to prompt

* DGTAI-39 Ultimate Compliance examples upload

* Improve DB fetch latency

* Standardize equality check of two DFs

* Fix left join for insert examples

* Improve equality check

* Improve similarity test

* Update ITEM_MASTER schema

* Update comments

* Add log info measuring times

* DGTAI-26 - Configure and run GUI engine on test server

* Update CLI to withstand test mode

* Fix issue where dates were not returned as strings

* Update example finder parameters

* Update schema representation

* Fix

* Fix bot answer

* DGTAI-39 Upload fourth batch of compliance examples

* DGTAI-39 Upload third batch of compliance examples

* DGTAI-39 Upload second compliance batch and compute new bot performance

* Force use of ISO 3166 country codes

* DGTAI-24 Add ITEM_COMPLIANCES DDL to relevant schemas

* DGTAI-39 Upload first batch of Compliance examples

* Update ITEM_MASTER schema

* DGTAI-27 Improve test examples correctness

* Delete ui instance variables

* Add update forbidden keyword

* Update GITIGNORE

* DGTAI-37 Add Agreement+Items test session results

* DGTAI-27 Fix issue in bot testing

Fix issue where test failed because of wrong data-type provided to equality check

* DGTAI-27 Increase number of examples fetched for prompt

* Add temporary safeguard to bot

* DGTAI-36 Upload new examples

Reformulate from blueprint examples
Upload examples to AI example-set
Update test set to include new example cases

* Change Flask app folder

* Fix

* DGTAI-36 Update examples

Update items examples with new "VALID" condition
Add new Agreement examples

* Thin out requirements

* DGTAI-34 Tweak LLM to improve results

* DGTAI-34 Run test on first agreement and items cases

* Imporve database connection

* DGTAI-34 Update examples to match DB changes

* Update tables schemas

* DGTAI-27 Deploy GPT with 16k tokens context

* Improve code readability

* Change weight of query examples in score computation

* DGTAI-27 Fix connection to database and data rendering in CLI

* DGTAI-33 Add metric computers for testing the BOT's performance

Modifed API to better suit test execution
Add tester file to measure bot's accuracy

* DGTAI-28 (wip) - UI integration

* Add blueprint file for private keys/info

* DGTAI-33 Split CompliBot API to UI

Add CompliBot class dedicated to handle API
Change CLI to call CompliBot API instead of directly implementing workflow

* DGTAI-33 Add client for connection to OCI

* Add OCI connection client

* Reformat prompt "new line policy"

* Reformat ITEM_MASTER

* DGTAI-32 Add test examples for Items

Reformat some queries for better adaptability to user input
Add test set examples in test set
Upload changes to queries to Azure AI Search

* DGTAI-29 Add test set for Agreement

Update old agreement examples
Add test examples
Change example uploader to dump test set

* DGTAI-28 (wip) - UI integration

* Improve tables schemas

Condensed DDLs for less verbose CREATE statements

* Update requirements.txt

* Update requirements.txt

* Clean config.ini

* Restructure project folders

* DGTAI-11 Refactor and embelish CLI

* Update tests

* DGTAI-11 Improve CLI output readability

* Add new ITEM_MASTER format to schema representation

* DGTAI-10 Reformat ITEM_MASTER schema for coherence

* DGTAI-10 Update Items schema

* DGTAI-10 Add invocation to final preliminary model

* DGTAI-10 Add Items schema to prompt

* DGTAI-14 Upload first batch of examples

* DGTAI-17 Add reference Items examples

* Add script to convert examples from Excel to JSON

* DGTAI-10 Refine the example selection

Add mean computation between inquiry and SQL similarity scores

* Update OpenAI API version

* DGTAI-11 Add CLI for inference execution

* Remove trailing spaces in schema representation

* DGTAI-11 Add logging to LLM client

* Change schema loader to fit JSON structure

* DGTAI-11 Change schema formatting for AI readability

* DGTAI-16 Change example format to uppercase for coherence

* DGTAI-16 Complete reformulation and upload examples

Complete the first agreements examples reformulation
Upload the reformulated examples on Azure AI Search (Update Parquet)

* DGTAI-10 Fix example retrival

Change embedding to binary BoW to count-BoW

* Delete redundant code

* Download Excel with questions for AI

* Add "FETCH" to keywords

* DGTAI-10 Update schema converter

Change database name for coherence

* DGTAI-10 Update example uploader

Add function to handle examples and local Parquet to keep track of examples already loaded

* DGTAI-16 Add agreement examples

Add 3 examples for each guideline

* Update gitignore

* DGTAI-10 Change "*_VAL_*" to "*_VALIDITY_*"

* DGTAI-16 Ready examples for synonym elaboration

* DGTAI-10 Change DB name in schema for coherence

* #DGTAI-10 Add OpenAI workaround

* Add JSON schema, add first example

* Update inquiry embedder and add examples

* Add CLI for preliminary model

* Add logging to preliminary model script

* Add connection to example index (Azure AI Search)

* Update config.ini with first Azure ML info

* Add logging to databases scripts, increase logging secruity to embedders

* Remove MATCH from reserved keywords

* Prepare connections for example set

* Add logging to schema fetcher, prune keywords, add mock example set

* Add logging to embedders

* Add logging to sql embedder and update requirements

* Sketch a possible call to the LLM

* Fix embedding connection

* Add test and debug preliminary model prompt building

* Add prompt creation function for preliminary model

* Add module to acquire DB metadata

* Remove debugging steps

* Change the scripts to support connection to Azure services

Change the inquiry embedder to connect to Azure OpenAI embedder
Change the search examples to connect to connect to examples DB

* Add dictionary generator

* Implement example retrival

Add class to handle the (query; search) examples retrival using Azure AI Search

* Add rudimentary DB representation

Add a JSON draft of a possible DB schema
Add a set of classes to represent the DB's entities.

* Add preliminary model handler

* Refactor package structure and draft preliminary model interactions and CLI

* Add inquiry embedder, configuration files, and CLI

Add inquiry embedder that calls Azure OpenAI servers
Add config.ini which contains parameters for connections
Add AIConfig.py to orchestrate configuration settings
Add setup.ps1 to initialise environment

* Delete skteches and add requirements

* Add SQL query binary embedder

* Update .gitignore

* Initial commit
