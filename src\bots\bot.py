from abc import ABC, abstractmethod
from itertools import tee
from re import sub, fullmatch
from time import sleep
from sqlglot.expressions import Table, Column

import sqlglot
import pandas as pd
import json
import os


from src.agents.agent import Agent
from src.agents.agent_factory import AgentFactory
from src.agents.answer import Answer
from src.agents.rag_agent.llm_models.metadata_extractor import MetadataExtractor

from src.bots.conversation_session import ConversationSession
from src.backend.contracts.chat_data import BotType
from src.backend.utils.uri_renderer import get_uri_renderers
from src.common_tools.history.history import ConversationHistory
from src.common_tools.intent_detector.intent_detector import IntentDetector
from utils.core import Singleton, get_logger
from utils.exceptions import BotValueException
from src.backend.contracts.chat_data import (
    SelectionObj,
    Answer,
    BotType,
    ChatResponse,
    File,
    SQLAnswerType
)

logger = get_logger(__file__)


class Bot(ABC):
    def __init__(
        self, agents, bot_name: str, **kwargs
    ) -> None:
        """ The executor class that has multple agents, based on the user intent, it calls agents that are associated to the bot """

        self._agents = []
        try:
            self._check_bot_name(bot_name)
            self.bot_name = bot_name
        except BotValueException as e:
            logger.error(f"{e.message}")

        agent_list = agents
        self._initialize_agent_list(agent_list, **kwargs)

    def _initialize_agent_list(self, agent_list, **kwargs):

        for agent in agent_list:
            self._agents.append(
                AgentFactory.create(
                    AgentFactory,
                    agent_name=agent,
                    bot_name=self.bot_name,
                    **kwargs
                )
            )

    def _check_bot_name(self, bot_name: str):
        if bot_name not in BotType.__members__:
            raise BotValueException(f"{bot_name} is not a valid name for a bot")

    def get_agent(self, agent_name: str) -> Agent:
        """ Return an agent  from the list of agents available given an agent name

        Args:
            agent_name (str): name of the agent
        Returns:
            agent (Agent): the agent class
        """
        agent = next(
            (agent for agent in self._agents if agent.agent_name == agent_name)
        )
        return agent

    def call_agent(self, agent_name: str, inquiry: str,history: ConversationHistory,  **kwargs) -> Answer:
        agent = self.get_agent(agent_name)
        return agent.ask(inquiry, history, **kwargs)

    def format_text_to_sql_answer(self, reply, sql_answer_type):

        header = reply.data.pop(0)  # Extract the header
        # Create the table body
        df = pd.DataFrame(data=reply.data, columns=header)
        if sql_answer_type == SQLAnswerType.MARKDOWN.value:
            formatted_answer = df.to_markdown(index=False)
            answer_type = SQLAnswerType.MARKDOWN.value
        elif sql_answer_type == SQLAnswerType.HTML.value:
            column_formatters = self.render_special_columns(reply.sql)
            for column_name, renderer_function in column_formatters.items():
                df[column_name] = df.apply(renderer_function, axis=1)

            formatted_answer=df.to_html(index=False, escape=False)
            answer_type = SQLAnswerType.HTML.value
        elif sql_answer_type == SQLAnswerType.JSON.value:
            formatted_answer = json.loads(df.to_json(orient='split'))
            answer_type = SQLAnswerType.JSON.value

        return formatted_answer, answer_type


    def render_special_columns(self,query):
        special_format_columns = {}
        schema_db = self._get_text_to_sql_bot_schema()

        parsed_tables = self._get_tables_and_columns_from_query(query)

        for table_parsed, column_list in parsed_tables.items():

            table_name = 'get_hierarchy_data' if fullmatch(r'^h\d*$', table_parsed) else table_parsed.upper()
            table_in_schema = next((table for table in schema_db.tables() if table.name == "DWH_PUBLIC." + table_name), None)

            for column_parsed in column_list:
                column_in_schema = next((column for column in table_in_schema.columns() if column.name == column_parsed.name.upper()), None)
                if column_in_schema:
                    if column_in_schema.metadata :
                        renderer = get_uri_renderers(column_in_schema.metadata["renderer_function"])
                        special_format_columns[column_parsed.name.upper()] = lambda x: renderer(x)

        return special_format_columns

    def _get_text_to_sql_bot_schema(self):
        text_to_sql_agent = self.get_agent(AgentFactory.TEXT_TO_SQL.name)
        schema = text_to_sql_agent.llm.schema
        return schema

    def _get_tables_and_columns_from_query(self, query: str) -> dict:
        parsed_query = sqlglot.parse_one(query, dialect="oracle")

        table_list_in_query = list(parsed_query.find_all(Table))

        if len(table_list_in_query) == 1:
            tables_and_column_map = {
                table.name: [
                    column for column in parsed_query.find_all(Column)
                ]
                for table in parsed_query.find_all(Table)
            }
        else:
            tables_and_column_map = {
                    (table.name or table.alias):  [
                        column for column in parsed_query.find_all(Column)
                        if column.table == table.name or column.table == table.alias
                    ]
                    for table in parsed_query.find_all(Table)
            }

        return tables_and_column_map

    @abstractmethod
    def handle(self,web_response: ChatResponse,
               history: ConversationHistory = None,
               conversation_session: ConversationSession = None, bot_config_handler = {}):
        pass


class BotHandlerFactory(metaclass=Singleton):
    def __init__(self, langchain_llm, azure_llm, text_to_sql_embedder, knowledge_bases):
        self.langchain_llm = langchain_llm
        self.azure_llm = azure_llm
        self.text_to_sql_embedder = text_to_sql_embedder
        self.knowledge_bases = knowledge_bases

    def get_handler(self, bot_name: str):
        match bot_name:

            case BotType.CALL_CENTER_BOT.name:
                return CallCenterBot(agents=[AgentFactory.TEXT_TO_SQL.name, AgentFactory.RAG.name, AgentFactory.RAG_DOCUMENT.name],
                                                bot_name=BotType.CALL_CENTER_BOT.name,
                                                model=self.langchain_llm,
                                                azure_model=self.azure_llm,
                                                embedder_model= self.text_to_sql_embedder,
                                                knowledge_base = self.knowledge_bases.get(BotType.CALL_CENTER_BOT.name))

            case BotType.APPLICATION_HOWTO_BOT.name:
                return HowToBot(agents=[AgentFactory.RAG.name],
                                bot_name=BotType.APPLICATION_HOWTO_BOT.name,
                                model=self.langchain_llm,
                                knowledge_base=self.knowledge_bases.get(BotType.APPLICATION_HOWTO_BOT.name))

            case BotType.COMPLI_BOT.name:
                return CompliBot(agents=[AgentFactory.TEXT_TO_SQL.name],
                                 bot_name=BotType.COMPLI_BOT.name,
                                 azure_model=self.azure_llm,
                                 model = self.azure_llm,
                                 embedder_model=self.text_to_sql_embedder)

            case BotType.JDAILO.name:
                return JDAIlo(agents=[AgentFactory.RAG.name],
                                bot_name=BotType.JDAILO.name,
                                model=self.langchain_llm,
                                knowledge_base=self.knowledge_bases.get(BotType.JDAILO.name))

            case BotType.EPROEXCELLA_BOT.name:
                return EPROExcelLaBot(agents=[AgentFactory.EPROEXCELLA.name],
                                      bot_name=BotType.EPROEXCELLA_BOT.name)

            case BotType.SEO_BOT.name:
                return SEOBot(agents=[AgentFactory.RAG.name],
                              bot_name=BotType.SEO_BOT.name,
                              model=self.langchain_llm,
                              knowledge_base=self.knowledge_bases.get(BotType.SEO_BOT.name))

            case BotType.DUMMY.name:
                return DummyBot()
            case _:
                raise ValueError(f"Unsupported bot type: {bot_name}")


class CompliBot(Bot, metaclass=Singleton):

    def handle(self, question: str, web_response: ChatResponse, history: ConversationHistory,
               conversation_session: ConversationSession, bot_config_handler: dict):

        show_explanation = bot_config_handler.get("overrides")["show_explanation"]
        max_rows = bot_config_handler.get("max_rows")
        show_sql = bot_config_handler.get("overrides")["show_sql"]
        sql_answer_type = bot_config_handler.get("overrides")["answer_type"]

        reply = self.call_agent(
                    AgentFactory.TEXT_TO_SQL.name,
                    inquiry=question,
                    history=history,
                    max_rows=max_rows,
                    conversation_session=conversation_session,
                    show_explanation=show_explanation,
                    show_sql=show_sql)


        formatted_answer, answer_type = self.format_text_to_sql_answer(reply, sql_answer_type)

        web_response.classification = f"{
            reply.confidence*100:.0f}%" if reply else "🤯"

        web_response.answer = Answer(
            formatted_answer=formatted_answer,
            query=reply.sql,
            explanation=reply.explanation,
            answer_type = answer_type,
            agent =  AgentFactory.TEXT_TO_SQL.value
        )
        web_response.files = [File(name='FileName', path='FilePath')]

        return web_response, False, None, reply

        #current_app.logger.debug(f"Reply: {reply}") DA CHIAMARE DOPO HANDLE


class CallCenterBot(Bot, metaclass=Singleton):

    def handle(self, question: str, web_response: ChatResponse, history: ConversationHistory, conversation_session: ConversationSession, bot_config_handler: dict):

        show_explanation = bot_config_handler.get("overrides")["show_explanation"]
        max_rows = bot_config_handler.get("max_rows")
        show_sql = bot_config_handler.get("overrides")["show_sql"]
        sql_answer_type = bot_config_handler.get("overrides")["answer_type"]
        intent_detector = bot_config_handler.get("intent_detector")
        document_types = bot_config_handler.get("document_types")
        agent_name = bot_config_handler.get("agent_name")

        stream = bot_config_handler.get("stream")

        agent_name, intent_detected = self._determine_agent_to_call(history,intent_detector, question, agent_name)

        web_response.question = question
        question_status = None
        stream_answer = False
        reply = None
        if agent_name  == AgentFactory.RAG.value:
            reply, question_status, stream_answer = self.call_agent(
                AgentFactory.RAG.name, inquiry=question, history=history, stream=stream,  conversation_session= conversation_session, max_chunks= 5, show_explanation=show_explanation, document_types = document_types)
            if stream_answer:
                reply.data, answer = tee(reply.data, 2)
                reply.data = list(reply.data)

            web_response.answer = Answer(
                formatted_answer = answer if stream_answer else reply.data,
                explanation = reply.explanation,
                source_file = "\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents]),
                agent = AgentFactory.RAG.value
            )
            web_response.appendix = reply.appendix
            web_response.classification = f"{
                reply.confidence*100:.0f}%" if reply else "🤯"
            web_response.images = reply.images
            web_response.files = list(set([File(name= os.path.basename(doc.metadata["url"]), path = doc.metadata["url"]) for doc in reply.documents]))
            if reply.document_types:
                web_response.selection = SelectionObj(is_multiple=True, choices= reply.document_types)

        elif agent_name == AgentFactory.TEXT_TO_SQL.value:
            stream_answer = False
            intent_detected_query = intent_detector.detect_query_intent(question)

            if len(intent_detected_query) == 0:
                asking_for_implosion = False
            else:
                asking_for_implosion = True
                # metadata_extractor = MetadataExtractor(langchain_llm)
                # code_extracted = metadata_extractor.get_code_from_query(question)
                # sparse_or_item = metadata_extractor.get_item_or_sparse_from_query(question)

            reply = self.call_agent(
                AgentFactory.TEXT_TO_SQL.name,
                inquiry=question,
                history=history,
                max_rows=max_rows,
                conversation_session=conversation_session,
                show_explanation=show_explanation,
                show_sql=show_sql,
                specific_query=asking_for_implosion)
            web_response.classification = f"{
                reply.confidence*100:.0f}%" if reply else "🤯"

            formatted_answer, answer_type = self.format_text_to_sql_answer(reply, sql_answer_type)

            web_response.answer = Answer(
                formatted_answer=formatted_answer,
                query=reply.sql,
                explanation=reply.explanation,
                answer_type = answer_type,
                agent =  AgentFactory.TEXT_TO_SQL.value
            )
            web_response.files = []

        elif agent_name  == AgentFactory.RAG_DOCUMENT.value:
            stream_answer = False

            reply, question_status = self.call_agent(
                AgentFactory.RAG_DOCUMENT.name, inquiry=question, history=history, conversation_session= conversation_session, show_explanation=show_explanation)

            web_response.answer = Answer(
                formatted_answer=reply.data,
                explanation = reply.explanation,
                source_file = reply.source_file,
                agent = AgentFactory.RAG_DOCUMENT.value
            )
            web_response.classification = f"{
                reply.confidence*100:.0f}%" if reply else "🤯"
            web_response.images = reply.images
            web_response.source_file_details = reply.source_file_details

        elif agent_name == "to_choice":
            web_response.selection = SelectionObj(is_multiple=False, choices=intent_detected)
            web_response.answer = Answer(formatted_answer="Where should I search this information?", agent="to_choice")
            web_response.classification = "Not available"

        return web_response, stream_answer, question_status, reply

    def _determine_agent_to_call(self, history, intent_detector, question, agent_name):

        intent_detected = None
        if not agent_name and len(history.messages) > 0 and agent_name != AgentFactory.TEXT_TO_SQL.value: # In order to prevent an intent detection when the bot is asking for metadata.
            last_history_message = history.messages[-1]
            if last_history_message["role"] == "assistant" and last_history_message["agent"] == 'RAG':
                agent_name = 'rag'
            elif last_history_message["role"] == "assistant" and last_history_message["agent"] == 'RAG_DOCUMENT':
                agent_name = 'rag_document'


        if agent_name == None: #Activate intent detecion
            intent_detected = intent_detector.detect_intent(question)
            if len(intent_detected) == 1:
                agent_name = intent_detected[0]
            elif len(intent_detected) == 0:  #If the intent is undetected, make a choice between all three agents
                agent_name = "to_choice"
                intent_detected = [{AgentFactory.TEXT_TO_SQL.name: AgentFactory.TEXT_TO_SQL.value}, {AgentFactory.RAG.name: AgentFactory.RAG.value}, {AgentFactory.RAG_DOCUMENT.name: AgentFactory.RAG_DOCUMENT.value}]
            else:
                agent_name = "to_choice"
                intent_detected = [{AgentFactory.reverse_agent_factory_mapping(agent_detected): agent_detected} for agent_detected in intent_detected]

        return agent_name, intent_detected

class JDAIlo(Bot, metaclass=Singleton):

    def handle(self,question: str, web_response: ChatResponse, history: ConversationHistory, conversation_session: ConversationSession,bot_config_handler: dict):

        show_explanation = bot_config_handler.get("overrides")["show_explanation"]
        stream_answer = bot_config_handler.get("stream")
        reply = self.call_agent(
            AgentFactory.RAG.name, inquiry=question, history=history, stream=stream_answer, max_chunks=5, conversation_session=conversation_session, show_explanation=show_explanation)
        if stream_answer:
            reply.data, answer = tee(reply.data, 2)
            reply.data = list(reply.data)
        web_response.answer = Answer(
            formatted_answer=answer if stream_answer else reply.data,
            explanation = reply.explanation,
            source_file = "\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents]),
            agent = AgentFactory.RAG.value
        )
        web_response.classification = f"{
            reply.confidence*100:.0f}%" if reply else "🤯"
        web_response.images = reply.images


        return web_response, stream_answer, None, reply

class SEOBot(Bot, metaclass=Singleton):

    def handle(self,question: str, web_response: ChatResponse, history: ConversationHistory, conversation_session: ConversationSession, bot_config_handler:dict):

        show_explanation = bot_config_handler.get("overrides")["show_explanation"]
        stream_answer = True
        reply = self.call_agent(
            AgentFactory.RAG.name, inquiry=question, history=history, stream=True, max_chunks=5, conversation_session=conversation_session, show_explanation=show_explanation)

        web_response.answer = Answer(
            formatted_answer=reply.data,
            explanation = reply.explanation,
            agent = AgentFactory.RAG.value,
            source_file = "\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents])
        )
        web_response.classification = f"{
            reply.confidence*100:.0f}%" if reply else "🤯"
        web_response.images = reply.images

        return web_response, stream_answer, None, reply

class HowToBot(Bot, metaclass=Singleton):

    def handle(self,question: str, web_response: ChatResponse, history: ConversationHistory, conversation_session: ConversationSession, bot_config_handler:dict):

        show_explanation = bot_config_handler.get("overrides")["show_explanation"]
        stream_answer = True
        reply = self.call_agent(
            AgentFactory.RAG.name, inquiry=question, history=history, stream=stream_answer, max_chunks=5, conversation_session=conversation_session, show_explanation=show_explanation)

        reply.data, answer = tee(reply.data, 2)
        reply.data = list(reply.data)
        web_response.answer = Answer(
            formatted_answer=reply.data,
            explanation = reply.explanation,
            source_file = "\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents])
        )
        web_response.classification = f"{
            reply.confidence*100:.0f}%" if reply else "🤯"
        web_response.images = reply.images

        return web_response, stream_answer, None, reply


class EPROExcelLaBot(Bot, metaclass=Singleton):

    def handle(self,question: str, web_response, history: ConversationHistory, conversation_session: ConversationSession, bot_config_handler: dict):

        pid = bot_config_handler.get("preview")
        user_id = bot_config_handler.get("user_id")
        reply = self.call_agent(AgentFactory.EPROEXCELLA.name, inquiry=question, history=history, preview=pid, user_id=user_id)
        data = pd.read_json(reply.data)
        data = data.head(10)
        web_response.answer = Answer(
            formatted_answer=data.to_html(index=False)
        )
        web_response.suggested_classification = "Not available"

        return web_response, False, None, reply

class DummyBot(Bot, metaclass=Singleton):

    def handle(self, question: str, web_response, history, conversation_session, bot_config_handler: dict):
        dialog_id = bot_config_handler.get("dialog_id")
        stream_answer = False
        if question == 'ping!5':
            sleep(5)
        return self.sample_answer(dialog_id=dialog_id, text=question), stream_answer, None, "test"

    def sample_answer(self, dialog_id: str, text: str):
        answer = Answer(
            formatted_answer="PONG - " + text,
            query="SELECT * FROM myTable;"
        )
        web_response = ChatResponse(
            dialog_id=dialog_id,
            answer=answer,
            classification="ANSWER",
            show_retry=False,
            suggested_classification=None,
            data_points=["abcd"],
            )
        return web_response
