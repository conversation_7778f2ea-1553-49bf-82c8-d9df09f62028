import json
from openai import AzureOpenAI
import time
import os
import tempfile
from typing import Dict
from flask import current_app
from pptx import Presentation
from pptx.enum.shapes import MSO_SHAPE_TYPE
from config.config import TranslatorBotConfig
from src.backend.blueprints.translator_bot.translator import Translator


class TranslationService:
    """
    Handles file translation using direct API calls to Azure OpenAI.
    """
    
    def __init__(self, user_id: str, original_filename: str = None):
        self.user_id = user_id
        # self.config = TranslatorBotConfig()
        self.translator = Translator()

        # Create independent upload directory for translator bot
        self.temp_base_dir = os.path.join(tempfile.gettempdir(), 'translator-bot-uploads')
        os.makedirs(self.temp_base_dir, exist_ok=True)
        self.upload_dir = os.path.join(self.temp_base_dir, f'user_{user_id}')
        os.makedirs(self.upload_dir, exist_ok=True)

        # Always set self.original_filename
        if original_filename is not None:
            self.original_filename = original_filename
        else:
            # Try to get secure filename from session if available
            from flask import session
            uploaded_file_info = session.get('uploaded_file_info', {})
            secure_filename_val = uploaded_file_info.get('secure_filename')
            self.original_filename = secure_filename_val

        self.file_path = os.path.join(self.upload_dir, self.original_filename)

        # # Initialize Azure OpenAI client using TranslatorBotConfig
        # self.client = AzureOpenAI(
        #     api_key=self.config.openai_llm_key,
        #     api_version=self.config.api_version,
        #     azure_endpoint=self.config.api_llm_endpoint,
        # )

        # # Store deployment name for later use
        # self.deployment_name = self.config.llm_deployed


    def translate_file(self, target_languages: list = None, file_context: str = None, selected_columns: list = None, session_id: str = None) -> dict:
        """
        Orchestrates translation for Excel, Word, or PowerPoint files.
        Handles both single and multiple target languages.
        """

        current_app.logger.info(f"Starting translation for user {self.user_id}, file: {self.original_filename}")
        
        # Validate file exists before proceeding
        if not os.path.exists(self.file_path):
            error_msg = f"File not found: {self.file_path}. The file may have been removed or the session expired."
            current_app.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'file': self.file_path,
                'target_languages': target_languages
            }
        
        handler_type = self.get_file_handler()
        result = {}
        
        # Use provided session_id or fall back to Flask session
        if session_id is None:
            from flask import session
            session_id = session.get('translation_session_id')
        
        try:

            # Select handler and get batches
            if handler_type == 'excel':
                from src.backend.blueprints.translator_bot.excel_handler import ExcelHandler
                handler = ExcelHandler(self.file_path)
                
                # Get columns if none selected
                if not selected_columns:
                    excel_info = handler.get_excel_info()
                    selected_columns = excel_info.get('col_names', [])
                    current_app.logger.info(f"No columns selected, using all columns: {selected_columns}")
                
                if not selected_columns:
                    raise Exception('No columns found in Excel file')

                # Perform translation
                for lang in target_languages:
                    current_app.logger.info(f"Starting translation for language: {lang}")
                    batches = handler.get_batches(selected_columns, max_rows=200)
                    batch_results = []
                    prompt = self.translator.get_default_prompt(lang)
                    
                    for batch in batches:
                        response = self.translator.submit_to_gpt(batch, prompt, file_context=file_context)
                        batch_results.append(response)
                    
                    # Write results to file
                    handler.write_results_to_file(batch_results, lang, selected_columns=selected_columns)
                    
            elif handler_type == 'word':
                from src.backend.blueprints.translator_bot.word_handler import WordHandler

                for lang in target_languages:
                    # Create a new handler instance for each language to avoid state issues
                    handler = WordHandler(self.file_path, file_context)
                    new_document = handler.translate_document(lang, False)
                    handler.write_result_to_file(new_document, lang)
                # Note: Word handler doesn't use progress tracking
                    
            elif handler_type == 'ppt':
                from src.backend.blueprints.translator_bot.pptx_handler import PPTXHandler

                for lang in target_languages:
                    # Create a new handler instance for each language to avoid state issues
                    handler = PPTXHandler(self.file_path, file_context)
                    new_presentation = handler.translate_presentation(lang)
                    handler.write_result_to_file(new_presentation, lang)
                # Note: PPT handler doesn't use progress tracking

            elif handler_type == '.pdf':

                from src.backend.blueprints.translator_bot.word_handler import WordHandler
                # Convert PDF to Word first
                initial_handler = WordHandler(self.file_path, file_context)
                self.file_path , is_pdf = initial_handler.convert_to_word()
                original_base, original_ext = os.path.splitext(self.file_path)
                self.original_filename = f"{original_base}{original_ext}"

                for lang in target_languages:
                    # Create a new handler instance for each language to avoid state issues
                    handler = WordHandler(self.file_path, file_context)
                    new_document = handler.translate_document(lang, is_pdf)
                    handler.write_result_to_file(new_document, lang)
            else:
                raise Exception(f"Unsupported file type: {self.original_filename}")

            result = {
                'success': True,
                'file': self.file_path,
                'target_languages': target_languages,
                'columns_translated': selected_columns
            }

            current_app.logger.info(f"Translation completed for {self.original_filename}")
            return result
        except Exception as e:
            current_app.logger.error(f"Translation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'file': self.file_path,
                'target_languages': target_languages
            }

    def cleanup(self):
        """
        Remove the user's temporary upload directory and all its contents.
        """
        try:
            import shutil
            if os.path.exists(self.upload_dir):
                shutil.rmtree(self.upload_dir)
                current_app.logger.info(f"Cleaned up temp directory for user {self.user_id}")
        except Exception as e:
            current_app.logger.error(f"Failed to clean up temp directory for user {self.user_id}: {e}")
        
    def get_file_handler(self):
        """
        Selects the appropriate file handler based on the file extension.
        """
        _, ext = os.path.splitext(self.original_filename)
        ext = ext.lower()
        if ext in ['.xlsx', '.xls']:
            return 'excel'
        elif ext == '.docx':
            return 'word'
        elif ext == '.pptx':
            return 'ppt'
        elif ext == '.pdf':
            return '.pdf'
        else:
            return 'unknown'



