[txt2sql.oracle_oci]
oci_client_path = C:\Users\<USER>\Oracle\instantclient_21_15
oci_dsn = dwhtest_high

[rag.parameters]
embedding_resource = https://epr-openai-sandbox-plus.openai.azure.com/
embedding_model = embedding-test-rag

[call_center_bot.rag.storage]
endpoint = https://epr-ai-srch-rag.search.windows.net
index_name = it-documents-pride-dev

[common]
run_mode = TEST
logout_url = https://electroluxprofessional.unily.com

[call_center_bot.rag.fetcher]
local_root =
pride_products_root = \\fs_prod.epr.electroluxprofessional.com\pride_fs_prod\Attach\
pride_document_types = PDS 

[how_to_bot.rag.fetcher]
local_root = ""
pride_products_root = 

[jdanallo.rag.storage]
endpoint = https://epr-ai-srch-rag.search.windows.net
index_name = jde-documents

[jdanallo.rag.fetcher]
local_root = C:\Users\<USER>\JDE_Documentation
pride_products_root = 

[jdanallo.rag.param]
semantic_configuration = semantic-jde
vector_search_profile = vector-profile-jde

[seobot.rag.fetcher]
local_root = C:\Users\<USER>\SEO_Documentation
pride_products_root = 

[seobot.rag.storage]
endpoint = https://epr-ai-srch-rag.search.windows.net
index_name = seo-bot-documents

[seobot.rag.param]
semantic_configuration = semantic-seo
vector_search_profile = vector-profile-seo

[log]
loglevel = DEBUG
log_folder = C:\Users\<USER>\Downloads\compli-bot\compli-bot\pride_indexer

[ad]
ad_schema_callback=http

[db]
db_driver = ODBC Driver 17 for SQL Server

[call_center_bot.azure_blob_storage]
account_url = https://epraisandboxsa.blob.core.windows.net/
account_name = epraisandboxsa
table_container = pride-tables-rag-dev
image_container = pride-images-rag-dev

[jdanallo.azure_blob_storage]
account_url = https://epraisandboxsa.blob.core.windows.net/
account_name = epraisandboxsa
table_container = jdallo-tables-rag-dev
image_container = jdallo-images-rag-dev

[seobot.azure_blob_storage]
account_url = https://epraisandboxsa.blob.core.windows.net/
account_name = epraisandboxsa
table_container = seo-bot-tables-rag
image_container = seo-bot-images-rag

[libreoffice]
libreoffice_path = D:\LibreOffice\program