import ast
import asyncio
from argparse import Argume<PERSON><PERSON><PERSON><PERSON>, Namespace
from concurrent.futures import Thr<PERSON>PoolExecutor
from time import time
from typing import Iterator, <PERSON>, Tuple, Union

from flask import current_app
from langchain_core.documents.base import Document
from langchain_core.pydantic_v1 import BaseModel, Field
from langchain_openai import AzureOpenAI
import re

from src.backend.contracts.chat_data import BotType, AgentName
from src.backend.contracts.chat_data import Agent<PERSON>ame

from src.agents.agent import Agent
from src.agents.answer import Answer

from src.agents.rag_agent.llm_models.generator import AugmentedGenerator
from src.agents.rag_agent.models import QuestionStatus, QuestionFoodLaundryCategory, DocumentStatusMapping, DistributionStatus, ProductionStatus
from src.agents.rag_agent.llm_models.metadata_extractor import MetadataExtractor
from src.agents.rag_agent.retriever import DocumentRetriever
from src.agents.rag_agent.function_utils import is_serial_number, is_new_chat_interaction, get_missing_metadata, get_initial_user_inquiry, check_missing_metadata, build_intermediate_answer
from src.agents.rag_agent.document_type_filter import DocumentType, reverse_mapping_document_type
from src.agents.rag_agent.models import QuestionStatus

from src.common_tools.history.history import ConversationHistory
from config.config import BlobStorageConfig
from utils.clients import AzureBlobClient, DatabaseClient
from utils.core import get_logger
from utils.exceptions import DocumentNotFoundException
from collections import Counter

logger = get_logger(__file__)

class RAGUtils():
    """
    Class to unify functios for the RAG and the RAGDocument agents
    """

    def __init__(self, bot_name: str, retriever: DocumentRetriever, generator: AugmentedGenerator, metadata_extractor: MetadataExtractor, storage_client: AzureBlobClient):
        self.bot_name = bot_name
        self.retriever = retriever
        self.generator = generator
        self.metadata_extractor = metadata_extractor
        self.storage_client = storage_client

        

        
    # Function for both agents   
    async def retrieval_augmented_process(self, question: str, agent_name: any, history: ConversationHistory = None, 
                                max_chunks: int = None, stream: bool = False, document_types = None, 
                                use_rag: bool = False) -> dict:
        

        """
        Function for both document retrieval and retrieval-augmented generation.
        
        Args:
            question: User question
            history: Conversation history
            max_chunks: Maximum number of chunks to retrieve (used only for RAG)
            stream: Whether to stream the response (used only for RAG)
            document_types: Optional document types to override existing ones
            use_rag: Whether to use RAG (True) or simple retrieval (False)
        
        Returns:
            Dict with answer, question status, and optionally stream flag for RAG

        For the RAG In this function there are two phases: Metadata extraction, and generating answer, this last phase is divided in retriving phase and generation phase
        """
        result = {'question_status': None}
        
        # Special case for RAG: followup question on the same topic
        if use_rag and is_followup_on_same_topic(history):
            result['question_status'] = QuestionStatus.FOLLOWUP_COMPLETE.value
            complete_metadata = history.messages[-1]["metadata_gathered"]
            
            answer = await self.search_and_generate_answer(question, max_chunks, complete_metadata, history, stream)
            
            result['answer'] = answer
            result['stream_answer'] = not answer[0]['STREAM'] == False
            return result
        
        # New chat interaction
        if is_new_chat_interaction(history, agent_name):
            result['question_status'] = QuestionStatus.QUERY.value
            
            # Extract metadata
            extracted_metadata = self.metadata_extractor.generate(question, missing_metadata=[])
            
            if re.match(r"\s*[A-Za-z]", extracted_metadata['product_code']):
                extracted_metadata['product_code'] = self.change_product_code(extracted_metadata['product_code'])

            # Handle document type based on function mode
            doc_types = self.metadata_extractor.extract_document_type(question).split(",")
            
            # Special case for RAG with multiple document types
            if use_rag and len(doc_types) > 1:
                extracted_metadata['document_type'] = doc_types
                missing_metadata = get_missing_metadata(extracted_metadata)
                intermediate_answer = check_missing_metadata(missing_metadata, extracted_metadata, doc_types)
                
                result['answer'] = intermediate_answer
                if use_rag:
                    result['stream_answer'] = False
                return result
            
            # For simple retrieval, use only the first document type
            if not use_rag:
                doc_types = [doc_types[0]]
                
            # Process metadata
            extracted_metadata["document_type"] = doc_types

            
            # Check for missing metadata
            missing_metadata = get_missing_metadata(extracted_metadata)
            intermediate_answer = check_missing_metadata(missing_metadata, extracted_metadata)
            
            # Return if metadata is incomplete
            if len(intermediate_answer) > 0:
                result['answer'] = intermediate_answer
                if use_rag:
                    result['stream_answer'] = False
                return result
            
            # Metadata is complete, process according to mode
            extracted_metadata["product_code"] = extracted_metadata["product_code"].split("*")[0]
            
            if use_rag:
                result['question_status'] = QuestionStatus.FOLLOWUP_COMPLETE.value
                answer = await self.search_and_generate_answer(question, max_chunks, extracted_metadata, history, True)
                result['answer'] = answer
                result['stream_answer'] = not answer[0]['STREAM'] == False
            else:
                document = await self.retrieve_document(extracted_metadata)
                result['answer'] = document
                
            return result
        
        # Followup for metadata gathering
        else:
            result['question_status'] = QuestionStatus.FOLLOWUP.value
            last_message = history.messages[-1]
            complete_metadata = last_message["metadata_gathered"]
            
            # Override document types if provided (RAG only)
            if document_types and use_rag:
                complete_metadata["document_type"] = document_types
            
            # Extract remaining metadata
            if len(last_message["missing_metadata"]) == 0:
                remaining_metadata = self.metadata_extractor.generate(question, ["product_code"])
            else:
                remaining_metadata = self.metadata_extractor.generate(question, last_message["missing_metadata"])
                
            
            # Filter metadata and update complete set
            remaining_metadata = {key: value for key, value in remaining_metadata.items() if value != "missing" or key in last_message["missing_metadata"]}
            missing_metadata = [key for key, value in remaining_metadata.items() if value == "missing"] 


            for key, value in remaining_metadata.items():
                complete_metadata[key] = value
            
            # Check for missing metadata
            intermediate_answer = check_missing_metadata(missing_metadata, complete_metadata)
            
            # Return if metadata is still incomplete
            if len(intermediate_answer) > 0:
                result['answer'] = intermediate_answer
                if use_rag:
                    result['stream_answer'] = False
                return result
            
            # Metadata complete, process according to mode
            complete_metadata["product_code"] = complete_metadata["product_code"].split("*")[0]
            
            if use_rag:
                result['question_status'] = QuestionStatus.FOLLOWUP_COMPLETE.value
                question_to_search = get_initial_user_inquiry(history)
                answer = await self.search_and_generate_answer(question_to_search, max_chunks, complete_metadata, history,stream=stream)
                result['answer'] = answer
                result['stream_answer'] = not answer[0]['STREAM'] == False
            else:
                document = await self.retrieve_document(complete_metadata)
                result['answer'] = document
                
                
            return result

    # Function for both agents   
    def _filter_duplicate_images(self, response: dict) -> list: 
            
        base_64_images = []
        for image_url in response["IMAGES"]:
            image, image_date = self.storage_client.read_image_blob(image_url)
            if image:
                image_url_no_uuid = "_".join(image_url.split("_")[1:])
                base_64_images.append((image_url_no_uuid, image_date, image))

        #FILTER DUPLICATE IMAGES
        sorted_images = sorted(base_64_images, key=lambda image: image[1], reverse=True)
        unique_image_names = {}

        for image_sorted in sorted_images:
            if image_sorted[0] not in unique_image_names:
                unique_image_names[image_sorted[0]] = image_sorted
        
        unique_image_list = list(unique_image_names.values())
        unique_image_list = sorted(unique_image_list, key=lambda image: image[1], reverse=False)
        unique_image_list = [image[2] for image in unique_image_list]

        return unique_image_list
    
    # Function for RAGDocument    
    async def retrieve_document(self, metadata: dict) -> dict:
        logger.debug(f'Fetching documents...')

        
        db_client = DatabaseClient()
            
        #this for managing different kind of products. SM is for service manual for Food products, SMA is for service manaul for laundry.
        document_type = metadata["document_type"]
        product_code = metadata["product_code"].upper()
        language = metadata["language_description"][0].upper() + metadata["language_description"][1:]
        document_number = metadata["document_number"].upper()

        edition = None
        production_status = None
        distribution_status = None
        
        if "edition" in metadata:
            edition = metadata["edition"]
        if "document_number" in metadata and metadata["document_number"] != "missing":
            document_number = metadata["document_number"]
        if "production_status" in metadata and metadata["production_status"] != "missing":
            production_status = metadata["production_status"]
        if "distribution_status" in metadata and metadata["distribution_status"] != "missing":
            distribution_status = metadata["distribution_status"]            
        
        documents_fetched, to_switch_product_code_and_document_number = self.get_document_and_metadata(product_code, document_type, edition, document_number, language, production_status, distribution_status, db_client)
        db_client.shutdown()
        documents_fetched_json, source_file_details = self.build_document_json(documents_fetched, language)

        if to_switch_product_code_and_document_number:
            metadata["product_code"], metadata["document_number"] = metadata["document_number"], metadata["product_code"]
            product_code, document_number = document_number, product_code
            
        document_image_list = None
        if document_type == DocumentType.EWD.name:

            document_image_list = await self.retriever.get_document_image_list(document_type,product_code,document_number, documents_fetched_json)
        
    
        document_retrieved_response = self.build_document_answer(documents_fetched_json, source_file_details, metadata, document_image_list)
        return document_retrieved_response

    # Function for RAGDocument    
    def get_document_and_metadata(self, product_code: str, document_type: str, edition: str, document_number: str, language: str, production_status: str, distribution_status: str, db_client: DatabaseClient) -> Tuple:

        documents_fetched = self._attempt_document_search_fallbacks(
            product_code, document_type, edition, document_number,production_status,distribution_status,
            [(language, "requested language") , ("English", "English"), ("Common", "Multilingual")],
            db_client
        )

        if not documents_fetched:
            if document_type not in {'SM', 'IN', 'SMA', 'OM'}:
                raise DocumentNotFoundException(
                            f"Sorry, the document for product you are asking for is not found in the database. "
                            f"Please check if the product code provided is correct."
                        )
            else:
                documents_fetched, to_switch_product_code_and_document_number = self._attempt_document_search_fallbacks(
                        product_code, DocumentType.HB.name, None, document_number, production_status, distribution_status,
                        [(language, "requested language"), ("English", "English"), ("Multilingual", "Multilingual")],
                        db_client
                    )
                if not documents_fetched:
                    raise DocumentNotFoundException(
                        f"Sorry, the document for product you are asking for is not found in the database. "
                        f"Please check if the product code provided is correct."
                    )
        elif documents_fetched == (None, False):
            raise DocumentNotFoundException(
                        f"Sorry, the document for product you are asking for is not found in the database. "
                        f"Please check if the product code provided is correct."
                    )

        return documents_fetched
    
    # Function for RAGDocument
    def build_document_answer(self, document_json: dict, source_file_details: dict, metadata: dict, document_image_list = None, ) -> dict:

        answer = f"""{f"Here the informations and the link to download the document provided:\n" if document_json["language_requested_found"] else "The document in the language requested is not found, here the informations and the download link for the english version:\n"}"""
        answer += ''.join(
            f"""- "{metadata}": {description}\n"""
            for metadata, description in document_json.items()
            if metadata not in ("file_url", "language_requested_found")
        )
        source_file = "\\" + document_json["file_url"].replace("/", "\\")
        
        response = {"ANSWER": answer, "SOURCE_FILE_DETAILS": source_file_details, "DOCUMENT": source_file, "EXPLANATION": '', "METADATA_GATHERED": metadata, "MISSING_METADATA": {}}
        if document_image_list:
            response["IMAGES"] = document_image_list
        return response
    
    # Function for RAGDocument
    def _attempt_document_search_fallbacks(self, product_code: str, document_type: str, edition: str, document_number: str, production_status: str, distribution_status: str, filters: Tuple[str,str], db_client: DatabaseClient):
        """
        Attempt to fetch a document using a sequence of language filters.

        :param product_code: str, product identifier.
        :param document_type: str, type of document.
        :param edition: str, specific edition requested (can be None).
        :param document_number: str, document number for query.
        :param filters: list of tuples (filter_query, description)
        :return: tuple (edition, language) or (None, None) if not found.
        """

        """first series of attempt is tryed with keeping the document number and prodcut code guessed by the AI, 
           if it gives no result, the AI misunderstood product code with document number so we retry the fallback with product code and 
           document_nubmer variables inverted, if this second attempt give result, we return true in the third boolean, so that the filter 
           in the azure search can be adjusted properly. """
    
        
        for language_filter_query, description in filters:
            query = self.build_initial_language_query(language_filter=language_filter_query, product_code=product_code, document_type=document_type, edition=edition, document_number=document_number, production_status=production_status, distribution_status=distribution_status, isString=False)
            documents = db_client.execute(query, numrows=None)
            documents_wo_header = documents[1:]
            isString = self.check_document_editon(documents_wo_header)
            
            query = self.build_language_query(language_filter=language_filter_query, product_code=product_code, document_type=document_type, edition=edition, document_number=document_number, production_status=production_status, distribution_status=distribution_status, isString=isString)
            documents = db_client.execute(query, numrows=None)  
            
            if documents and len(documents) > 1:
                return documents, False # Return edition and language

        #second attempt with value inverte    
        for language_filter_query, description in filters:
            query = self.build_initial_language_query(language_filter=language_filter_query, product_code=document_number, document_type=document_type, edition=edition, document_number=product_code, production_status=production_status, distribution_status=distribution_status, isString=False)
            documents = db_client.execute(query, numrows=None)
            documents_wo_header = documents[1:]
            isString = self.check_document_editon(documents_wo_header)

            query = self.build_language_query(language_filter=language_filter_query, product_code=product_code, document_type=document_type, edition=edition, document_number=document_number, production_status=production_status, distribution_status=distribution_status, isString=isString)
            documents = db_client.execute(query, numrows=None) 

            if documents and len(documents) > 1:
                return documents, True # Return edition and language
            
        return None,False  # Return nothing if all fallbacks fail
    
    def check_document_editon(self, documents):
        isString = False
        for row in documents:
            edition = row[0].strip()
            if not edition.isdigit():
                isString = True
                break
        return isString


    def build_initial_language_query(self, language_filter, product_code:str, document_type, edition = None, document_number = None, production_status = None, distribution_status = None, isString = False):
        document_type_filter = self.build_document_type_filter(document_type[0])
        production_distribution_filter = self.build_production_distribution_filter(production_status, distribution_status)
        initial_query = f"""SELECT DOCUMENT_EDITION, LANGUAGE_DESCR, FILE_PATH, DOCUMENT_TYPE_DESCR, DOCUMENT_DATE, DOCUMENT_STATUS, DOCUMENT_TYPE, DOCUMENT_NUMBER, ID_FILE, LANGUAGE, INTERNAL_CODE
                            FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS 
                            WHERE {f"(INTERNAL_CODE = '{product_code}' OR PRODUCT_NUMBER = '{product_code}' OR FACTORY_MODEL = '{product_code}')" if product_code.lower() != "missing" else f"DOCUMENT_NUMBER='{document_number}'"}  
                            {document_type_filter}
                            {production_distribution_filter}
                            {f"AND DOCUMENT_EDITION='{edition}'" if edition else ''} 
                            AND (LANGUAGE_DESCR = '{language_filter}' OR LANGUAGE_DESCR = '{language_filter} US' OR LANGUAGE_DESCR = 'Multilingual')
                            """
        return initial_query

    # Function for RAGDocument
    def build_language_query(self, language_filter, product_code:str, document_type, edition = None, document_number = None, production_status = None, distribution_status = None, isString = False):

        document_type_filter = self.build_document_type_filter(document_type[0])
        production_distribution_filter = self.build_production_distribution_filter(production_status, distribution_status)
        if not isString:
            query_db = f"""SELECT DOCUMENT_EDITION, LANGUAGE_DESCR, FILE_PATH, DOCUMENT_TYPE_DESCR, DOCUMENT_DATE, DOCUMENT_STATUS, DOCUMENT_TYPE, DOCUMENT_NUMBER, ID_FILE, LANGUAGE, INTERNAL_CODE
                            FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS 
                            WHERE {f"(INTERNAL_CODE = '{product_code}' OR PRODUCT_NUMBER = '{product_code}' OR FACTORY_MODEL = '{product_code}')" if product_code.lower() != "missing" else f"DOCUMENT_NUMBER='{document_number}'"}  
                            {document_type_filter}
                            {production_distribution_filter}
                            {f"AND DOCUMENT_EDITION='{edition}'" if edition else ''} 
                            AND (LANGUAGE_DESCR = '{language_filter}' OR LANGUAGE_DESCR = '{language_filter} US' OR LANGUAGE_DESCR = 'Multilingual')
                            {f"ORDER BY CAST(DOCUMENT_EDITION AS DECIMAL(10,2))  DESC" if document_type != 'SPC' else f"ORDER BY DOCUMENT_DATE DESC"}
                        """
        else:
            query_db = f"""SELECT DOCUMENT_EDITION, LANGUAGE_DESCR, FILE_PATH, DOCUMENT_TYPE_DESCR, DOCUMENT_DATE, DOCUMENT_STATUS, DOCUMENT_TYPE, DOCUMENT_NUMBER, ID_FILE, LANGUAGE, INTERNAL_CODE
                            FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS 
                            WHERE {f"(INTERNAL_CODE = '{product_code}' OR PRODUCT_NUMBER = '{product_code}' OR FACTORY_MODEL = '{product_code}')" if product_code.lower() != "missing" else f"DOCUMENT_NUMBER='{document_number}'"}  
                            {document_type_filter}
                            {production_distribution_filter}
                            {f"AND DOCUMENT_EDITION='{edition}'" if edition else ''} 
                            AND (LANGUAGE_DESCR = '{language_filter}' OR LANGUAGE_DESCR = '{language_filter} US' OR LANGUAGE_DESCR = 'Multilingual')
                            {f"ORDER BY DOCUMENT_DATE DESC"}
                        """
        return query_db
    
    # Function for RAGDocument
    def build_document_type_filter(self, document_type: str) -> str:

        document_type_supported = DocumentType[document_type]
        document_type_supported_list = ",".join([f"'{element}'" for element in document_type_supported])
        document_type_filter = f"AND DOCUMENT_TYPE IN ({document_type_supported_list})"

        return document_type_filter
    
    # Function for RAGDocument
    def build_production_distribution_filter(self, production_status, distribution_status) -> str:

        def no_one_status() -> bool:
            return not production_status and not distribution_status
        
        def only_production_status() -> bool:
            return production_status and not distribution_status
        
        def only_distribution_status() -> bool:
            return not production_status and distribution_status
        
        def both_status() -> bool:
            return production_status and distribution_status
        

        if no_one_status():
            return ''
        if only_production_status():
            production_status_code = ProductionStatus.from_code(production_status).description
            if production_status_code == ProductionStatus.IN_PRODUCTION.name:
                production_distribution_filter = f"AND DOCUMENT_STATUS IN ('{DocumentStatusMapping.from_code(1).code}','{DocumentStatusMapping.from_code(3).code}')"
            else:
                production_distribution_filter = f"AND DOCUMENT_STATUS IN ('{DocumentStatusMapping.from_code(2).code}','{DocumentStatusMapping.from_code(4).code}')"
        if only_distribution_status():
            distribution_status_code = DistributionStatus.from_code(distribution_status).description
            if distribution_status_code == DistributionStatus.IN_DISTRIBUTION.name:
                production_distribution_filter = f"AND DOCUMENT_STATUS IN ('{DocumentStatusMapping.from_code(1).code}','{DocumentStatusMapping.from_code(4).code}')"
            else:
                production_distribution_filter = f"AND DOCUMENT_STATUS IN ('{DocumentStatusMapping.from_code(2).code}','{DocumentStatusMapping.from_code(3).code}')"
        if both_status():
            production_status_code = str(ProductionStatus.from_code(production_status).code)
            distribution_status_code = str(DistributionStatus.from_code(distribution_status).code)

            status_map = production_status_code + distribution_status_code

            document_status_code_mapped = DocumentStatusMapping.from_map(status_map).code
            production_distribution_filter = f"AND DOCUMENT_STATUS = '{document_status_code_mapped}'"
        
        return production_distribution_filter 
        
    # Function for RAGDocument
    def build_document_json(self, documents_fetched: List[Tuple], language) -> List[dict]:
        document_language_requested_found = True
        language_found = documents_fetched[1][1]

        if language_found != language:
            document_language_requested_found = False
        
        document_object = {
                            'document_edition': documents_fetched[1][0],
                            'language': documents_fetched[1][1],
                            'file_url': documents_fetched[1][2],
                            'document_type': documents_fetched[1][3],
                            'document_date': documents_fetched[1][4],
                            'document_status': DocumentStatusMapping.from_code(int(documents_fetched[1][5])).description,
                            'language_requested_found': document_language_requested_found,
                            } 
        
        source_file_details = {
            'document_edition': documents_fetched[1][0],
            "document_type": reverse_mapping_document_type(documents_fetched[1][6]),
            "document_number": documents_fetched[1][7],
            "id_file": documents_fetched[1][8],
            "language_id": documents_fetched[1][9],
            "internal_code": documents_fetched[1][10],
        }

        return document_object, source_file_details
    
    # Function for RAG for all bots
    async def search_and_generate_answer(self, question: str, max_chunks: int, metadata: dict, history: ConversationHistory, stream: bool) -> Tuple[Union[Iterator[str], str], List[Document]]:
        
        #retrieving phase
        result = await self.retriever.search(question, max_chunks, history, metadata)
        docs = result["highlights"]

        if self.bot_name == BotType.CALL_CENTER_BOT.name:
            if len(docs) == 0:
                return build_intermediate_answer("No text chunks were found for the requested document. Please check if the metadata (document type, edition, language, product code) is correct.", missing_metadata={}, document_metadata=metadata)  
            to_translate = result["to_translate"]
        else:
            to_translate=False

        #generating phase

        reply_stream = self.generator.generate(question, docs, to_translate, blob_client=self.storage_client, bot_name = self.bot_name, isContext=False, stream=stream)

        start = time()
        with ThreadPoolExecutor() as executor:
            future = executor.submit(self.get_context, question, docs, to_translate)  # Run `context_stream` in parallel
            response = future.result()

        end = time() - start
        current_app.logger.debug(f"Time Thread execution: {end}")

        if self.bot_name == BotType.CALL_CENTER_BOT.name:
            response["METADATA_GATHERED"] = metadata
        response["MISSING_METADATA"] = []
        response["STREAM"] = stream

        if stream:
            answer = list(reply_stream)
        else:
            answer = reply_stream
        response["ANSWER"] = answer

        return (response, docs)
        
    def get_context(self, question, docs, to_translate):
        
        context = self.generator.generate(
            question, docs, to_translate, self.storage_client, bot_name=self.bot_name, isContext=True, stream=False
        )
        return context
    
    def change_product_code(self, product_code:str):
        db_client = DatabaseClient()
        code = product_code.split("*")[0]
        query_sfather = f"""SELECT SFATHER
                    FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS 
                    WHERE {f"(INTERNAL_CODE = '{code}')"}  
                """
        result = db_client.execute(query_sfather, numrows=None)
        if len(result) < 2:
            query_factory_model = f"""SELECT FACTORY_MODEL
                    FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS 
                    WHERE {f"(INTERNAL_CODE = '{code}')"}  
                """
            result = db_client.execute(query_factory_model, numrows=None)
            if len(result) < 2:
                db_client.shutdown
                return product_code
            else:
                factory_model =  result[1][0]
                query_sfathers = f"""SELECT DISTINCT INTERNAL_CODE, SFATHER 
                                        FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS
                                        WHERE INTERNAL_CODE IN (
                                            SELECT INTERNAL_CODE
                                            FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS
                                            WHERE {f"(FACTORY_MODEL = '{factory_model}')"}
                                        )
                                        OR PRODUCT_NUMBER IN (
                                            SELECT PRODUCT_NUMBER
                                            FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS
                                            WHERE {f"(FACTORY_MODEL = '{factory_model}')"}
                                        )
                """
                result = db_client.execute(query_sfathers, numrows=None)
                if len(result) < 2:
                    db_client.shutdown
                    return product_code
                else:
                    product_code = self.most_frequent(result)
                    db_client.shutdown
                    return product_code
        else:
            new_product_code = result[1][0]
            db_client.shutdown()
            new_product_code = new_product_code + "*product_code"
            return new_product_code

    def most_frequent(self, results: List[Tuple]):
        results_wo_headers = results[1:]
        sfathers = [data[1] for data in results_wo_headers]
        count = Counter(sfathers)
        most_frequent, occurrences = count.most_common(1)[0]
        return most_frequent


# Function for RAG            
def is_followup_on_same_topic(history: ConversationHistory) -> bool:
    return len(history.messages) > 0 and history.messages[-1]["agent"] != AgentName.TEXT_TO_SQL.name and history.messages[-2]["question_status"] == "followup_complete"

# Function for RAGDocument
def is_laundry_file(document_type: str, category: QuestionFoodLaundryCategory):
    return category == QuestionFoodLaundryCategory.LANUDRY.value and document_type == DocumentType.PRF.name

def check_metadata(rag_answer, document_object):
    if "METADATA_GATHERED" in document_object.keys():
        rag_answer.add_metadata_gathered(document_object["METADATA_GATHERED"])
        rag_answer.add_missing_metadata(document_object["MISSING_METADATA"])


        if document_object["EXPLANATION"] == "metadata-incomplete":
            rag_answer.add_status("asking_metadata")
        else:
            rag_answer.add_status("complete")   

    return rag_answer




