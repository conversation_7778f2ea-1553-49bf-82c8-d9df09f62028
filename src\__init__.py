# init.py
import os, socket, certifi, httpx, logging, threading
from logging.config import dictConfig
from flask import Flask, render_template, g, session
from flask_migrate import Migrate
from flask_session import Session
from flask_sqlalchemy import SQLAlchemy
from langchain_openai import AzureOpenAI
from utils.clients import DatabaseClient, TranslatorAzureLLM
from config.config import CommonConfig, Text2SQLConfig, TranslatorBotConfig
from utils.clients import <PERSON>zureLLM, LangChainLLM, AzureEmbedder, KnowledgeBaseRegistry
from src.backend.utils.app_config import AppConfig

cfg = CommonConfig()
oracle_cfg = Text2SQLConfig()
translator_config = TranslatorBotConfig()
log_folder = os.path.join(cfg.log_folder, socket.gethostname())

# Create the log folder if it doesn't exist
os.makedirs(log_folder, exist_ok=True)

loglevel_from_config = cfg.log_level

dictConfig(
    {
        "version": 1,
        "formatters": {
            "default": {
                "format": "[%(asctime)s] [%(username)s] {%(thread)d.%(module)s.%(funcName)s:%(lineno)d} %(levelname)s: %(message)s",
            }
        },
        "filters": {
            "user_context": {
                "()": "src.common_tools.log_utils.log_utils.UserContextFilter"
            }
        },
        "handlers": {
            "wsgi": {
                "class": "logging.StreamHandler",
                "stream": "ext://flask.logging.wsgi_errors_stream",
                "formatter": "default",
                "level": "DEBUG",
                "filters": ["user_context"],
            },
            "file": {
                "class": "concurrent_log_handler.ConcurrentRotatingFileHandler",
                "formatter": "default",
                "filename": os.path.join(log_folder, "epro-bot.log"),
                "maxBytes": 50000000,
                "backupCount": 2,
            },
        },
        "loggers": {
            "": {"level": loglevel_from_config, "handlers": ["wsgi", "file"]},
            "werkzeug": {"level": "WARNING", "handlers": ["wsgi", "file"], "propagate": False},
            "flask.app": {"level": loglevel_from_config, "handlers": ["wsgi", "file"], "propagate": False},
            "models": {"level": loglevel_from_config, "propagate": True},
            "database": {"level": loglevel_from_config, "propagate": True},
            "embedders": {"level": loglevel_from_config, "propagate": True},
            "clients": {"level": loglevel_from_config, "propagate": True},
            "src": {"level": loglevel_from_config, "propagate": True},
            "rag": {"level": loglevel_from_config, "propagate": True},
            "history": {"level": loglevel_from_config, "propagate": True},
        },
    }
)


for name, logger in logging.root.manager.loggerDict.items():
    if isinstance(logger, logging.Logger):
        print(f"Setting logger level to {loglevel_from_config} for logger {name}")
        for handler in logger.handlers:
            print(f" Handler: {handler}")
            print(f" Formatter: {handler.formatter._fmt if handler.formatter else 'None'}")

# Force werkzeug loglevel to WARNING to avoid too many debug logs
werkzeug_logger = logging.getLogger("werkzeug")
werkzeug_logger.setLevel(logging.WARNING)

logging.getLogger().info("🚀 INIT App START 🚀")

logging.getLogger().debug("Test DEBUG log message")
logging.getLogger().info("Test INFO log message")
logging.getLogger().warning("Test WARNING log message")
logging.getLogger().error("Test ERROR log message")


oracle_db = DatabaseClient()

db = SQLAlchemy()


def create_app():
    app = Flask(
        __name__, template_folder="backend/templates", static_folder="backend/static"
    )

    app.config["SECRET_KEY"] = str(cfg.flask_secret_key)
    # papp.config['SQLALCHEMY_DATABASE_URI'] = f'mssql+pyodbc:///?odbc_connect=DRIVER={cfg.db_driver};SERVER={cfg.db_host};DATABASE={cfg.db_name};UID={cfg.db_user};PWD={cfg.db_password};TrustServerCertificate=yes'
    app.config["SQLALCHEMY_DATABASE_URI"] = (
        f"mssql+pymssql://{cfg.db_user}:{cfg.db_password}@{cfg.db_host}/{cfg.db_name}?charset=utf8"
    )
    app.config["SQLALCHEMY_BINDS"] = {
        "sql_server" : f"mssql+pymssql://{cfg.db_user}:{cfg.db_password}@{cfg.db_host}/{cfg.db_name}?charset=utf8",
        "oracle": f"oracle+oracledb://{oracle_cfg.oci_username}:{oracle_cfg.oci_password}@{oracle_cfg.oci_dsn}"
    }
    app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False  # Recommended for performance
    migrate = Migrate(app, db)

    db.init_app(app)

    app.config["LANGCHAIN_LLM"] = None
    app.config["AZURE_LLM"] = None
    app.config["TEXT_TO_SQL"] = None
    app.config["KNOWLEDGE_BASE"] = None
    app.config["AI_READY"] = False

    app.config["SESSION_SQLALCHEMY"] = db
    app.config["SESSION_TYPE"] = "sqlalchemy"
    # app.config['SESSION_SQLALCHEMY_TABLE'] = 'session'
    app.config["SESSION_PERMANENT"] = True  # Optional, for persistent sessions
    app.config["SESSION_USE_SIGNER"] = True  # Optional, for signed sessions (security)
    app.config["AI_CONFIG"] = cfg

    Session(app)

    # Register blueprint for auth
    auth_config = {
        "TENANT": cfg.ad_authority_uri,
        "CLIENT_ID": cfg.ad_client_id,
        "CLIENT_SECRET": cfg.ad_secret,
        "HTTPS_SCHEME": cfg.ad_schema_callback,
    }

    from .cli import bp as cli_bp
    app.register_blueprint(cli_bp)

    from .backend.blueprints import auth
    app.register_blueprint(auth.construct_blueprint(auth_config), url_prefix="/auth")

    from .backend.blueprints.rest import api_v1 as rest_blueprint
    app.register_blueprint(rest_blueprint)

    # blueprint for react frontend routes
    from .backend.rbot.routes import rbot as rbot_blueprint
    app.register_blueprint(rbot_blueprint)

    # blueprint for non-auth parts of app
    from .backend.main import main as main_blueprint
    app.register_blueprint(main_blueprint)

    # blueprint for admin pages
    from .backend.blueprints.rbot_admin import rbot_admin as rbot_admin_blueprint
    app.register_blueprint(rbot_admin_blueprint, url_prefix='/admin')

    # blueprint for translation tool
    from .backend.blueprints.translator_bot import translator_bot as translator_bot_blueprint
    app.register_blueprint(translator_bot_blueprint, url_prefix='/translator')

    @app.before_request
    def set_user_in_g():
        if 'user' in session:
            g.user = session['user'].get('username', 'anonymous')
        else:
            g.user = 'anonymous'

    @app.before_request
    def check_maintenance_mode():
        from flask import request, jsonify, url_for
        config_manager = AppConfig.get_instance()
        is_down_for_maintenance = config_manager.get("is_down_for_maintenance", "false").lower() == "true"
        is_down_for_maintenance_api = config_manager.get("is_down_for_maintenance_api", "false").lower() == "true"

        # Escludi le richieste alla cartella static
        if request.path.startswith('/static/'):
            return None

        if (
            (is_down_for_maintenance and not request.path.startswith('/api/')) or
            (is_down_for_maintenance_api and request.path.startswith('/api/'))
        ):
            maintenance_page_url = "/static/maintenance.html"
            # Controlla se la richiesta è una chiamata API
            if request.is_json or request.path in ['/user-profiles', '/version', '/bot-list', '/search-settings']:
                return jsonify({
                    "error": "maintenance_mode",
                    "message": "Application is currently under maintenance",
                    "status": 503,
                    "redirectUrl": maintenance_page_url
                }), 503
            # In case o plain request redirect to html page
            return render_template("maintenance.html"), 503

    @app.context_processor
    def inject_current_user():
        return {"current_user": session.get("user")}

    threading.Thread(target=init_ai_services, args=(app,), daemon=True).start()

    logging.getLogger().info("🏁 INIT App END 🏁")

    return app


os.environ['SSL_CERT_FILE'] = certifi.where()


def init_ai_services(app):

    try:
        logging.getLogger().info("🚀 Init AI services start 🚀")
        langchain_llm = LangChainLLM(azure_endpoint=cfg.api_llm_endpoint,
                        azure_deployment=cfg.llm_deployed,
                        api_version=cfg.api_version,
                        api_key=cfg.openai_llm_key,
                        temperature=0)

        knowledge_base = KnowledgeBaseRegistry()

        azure_llm = AzureLLM(azure_endpoint=cfg.api_llm_endpoint,
                  azure_deployment=cfg.llm_deployed,
                  api_version=cfg.api_version,
                  api_key=cfg.openai_llm_key)

        text_to_sql_embedder = AzureEmbedder(
                    api_key=oracle_cfg.openai_embedding_key,
                    api_version=oracle_cfg.openai_api_version,
                    azure_endpoint=oracle_cfg.openai_embedding_endpoint,
                    http_client=httpx.Client(proxy=oracle_cfg.proxy))
        
        translator_llm = TranslatorAzureLLM(
                api_key=translator_config.translator_azure_ai_api_translator_key,
                api_version=translator_config.api_version,
                azure_endpoint=translator_config.api_llm_endpoint,
        )

        with app.app_context():
            app.config["LANGCHAIN_LLM"] = langchain_llm
            app.config["KNOWLEDGE_BASE"] = knowledge_base
            app.config["AZURE_LLM"] = azure_llm
            app.config["TEXT_TO_SQL"] = text_to_sql_embedder
            app.config["TRANSLATOR_LLM"] = translator_llm
            app.config["AI_READY"] = True

        logging.getLogger().info("🏁 Init AI services end 🏁")
    except Exception as e:
        logging.getLogger().error(f"❌ Error init AI services: {e}")


app = create_app()
