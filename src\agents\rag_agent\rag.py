import ast
import asyncio
from argparse import Argument<PERSON><PERSON><PERSON>, Namespace
from typing import Iterator, <PERSON>, Tuple, Union

from langchain_core.documents.base import Document
from pydantic import BaseModel, Field
from langchain_openai import AzureOpenAI

from src.backend.contracts.chat_data import Bot<PERSON>ype, AgentName
from src.backend.contracts.chat_data import AgentName

from src.agents.agent import Agent
from src.agents.answer import Answer

from src.agents.rag_agent.llm_models.generator import AugmentedGenerator
from src.agents.rag_agent.llm_models.metadata_extractor import MetadataExtractor
from src.agents.rag_agent.retriever import DocumentRetriever
from src.agents.rag_agent.rag_utils import RAGUtils, check_metadata

from src.bots.conversation_session import ConversationSession
from src.common_tools.history.history import ConversationHistory
from config.config import BlobStorageConfig
from utils.clients import AzureBlobClient, DatabaseClient

import asyncio

class RAGAnswer(Answer):
    data: str = None
    chapter: str = ''
    source_file: str = ''
    confidence: float = 1.0
    explanation: str = ''
    documents: List[Document] = None
    images: List[str] = []
    metadata_gathered: dict = {}
    missing_metadata: list = []
    status: str = ''
    agent_name: AgentName.RAG.value
    document_types:  List[dict] = None
    appendix: str = ''

    def __init__(self, data: str) -> None:
        self.data = data

    def add_chapter(self, chapter: str) -> None:
        self.chapter = chapter

    def add_source_file(self, source_file: List[str]) -> None:
        self.source_file = source_file

    def add_documents(self, documents: List[Document]) -> None:
        self.documents = documents

    def add_explanation (self, explanation: str) -> None:
        self.explanation = explanation


    def add_images(self, images: List[str]) -> None:
        self.images = images

    def add_metadata_gathered(self, metadata: dict) -> None:
        self.metadata_gathered = metadata

    def add_missing_metadata(self, missing_metadata: list) -> None:
        self.missing_metadata = missing_metadata

    def add_status(self, status: str) -> None:
        self.status = status

    def add_document_types(self, document_types: List[str]) -> None:
        self.document_types = document_types

    def add_appendix(self, appendix: str) -> None:
        self.appendix = appendix

    def to_json(self):
        return {
            "data": self.data,
            "source_file": self.source_file,
            "agent": self.agent_name,
            "confidence": 1.0,
            "explanation": self.explanation,
            "status": self.status,
            "missing_metadata": self.missing_metadata,
            "metadata_gathered": self.metadata_gathered,
            "document_types": self.document_types,
            "appendix": self.appendix
        }

class rag(BaseModel):
    """"This function should be called when the user asks questions that require a combination of information retrieval from technical documentation and manuals, followed by text generation to provide detailed and relevant answers. Typical queries might include:

        - Requests for detailed explanations ("Which are the Personal Protecion Equipment for the Oven of code 1233?", "Give me the table of the gas models for Oven 1233", "What are the installation step for this laundry?").
    """

    query: str =  Field(description=" The question that will be used as search query")

class RAG(Agent):
    def __init__(self, bot_name: str, name= AgentName.RAG.value, model = None, azure_model=None, embedder_model= None, knowledge_base = None) -> None:
        """Initialize the  RAG agent that has two main attributes: the retriever and the generator.

        - The retreiver is responsible for searcing relevant chunk of texts in the Azure AI search index based on user question
        - The generator give in input the chunk retrieved to the LLM in order to get the answer of the user

        """
        super().__init__(name, bot_name)
        self.retriever = DocumentRetriever(self.bot_name, knowledge_base)
        self.generator = AugmentedGenerator(self.bot_name, model)
        self.metadata_extractor = MetadataExtractor(model)
        blob_config = BlobStorageConfig()
        self.storage_client = AzureBlobClient(blob_config, self.bot_name)
        self.rag_utils = RAGUtils(self.bot_name, self.retriever, self.generator, self.metadata_extractor, self.storage_client)

    async def retrieval_augmented_generate_metadata(self, question: str, max_chunks: int, stream: bool, history: ConversationHistory = None, document_types = None) -> Tuple[Union[Iterator[str], str], List[Document]]:
       return await self.rag_utils.retrieval_augmented_process(
            question=question,
            agent_name=AgentName.RAG.name,
            history=history,
            max_chunks=max_chunks,
            stream=stream,
            document_types=document_types,
            use_rag=True
        )


    def ask(self, inquiry:str, history: ConversationHistory, stream:bool, max_chunks: int = 16, conversation_session: ConversationSession = None, show_explanation: bool = False, document_types: List[str] = None ) -> Tuple[RAGAnswer, str]:
        """There are two kind of searches implemented:
           - The CALL_CENTER_BOT search, where retrieve info inside the azure search index is supported by the extraction of some metadata required for better filtering chunks.
           - APPLICATION_HOWTO_BOT search, pure rag retrieve without filtering, need to implement filtering in this case too.
        """

        self.session = conversation_session
        self.session.checkpoint()

        #APPLICATION_HOW_TO_BOT search
        if self.bot_name == BotType.APPLICATION_HOWTO_BOT.name:
            metadata = {}
            response, docs = asyncio.run(
                self.rag_utils.search_and_generate_answer(inquiry, max_chunks, metadata, history, stream)
            )

            answer = response["ANSWER"]
            self.session.checkpoint()

            rag_answer = self.build_rag_answer(response, docs, answer, show_explanation)

            self.session.checkpoint()

            return rag_answer

        #JDAIlo and SEO_Bot search
        if self.bot_name != BotType.CALL_CENTER_BOT.name and self.bot_name != BotType.APPLICATION_HOWTO_BOT.name:
            metadata = {}
            response, docs = asyncio.run(
                self.rag_utils.search_and_generate_answer(inquiry, max_chunks, metadata, history, stream)
            )

            answer = response["ANSWER"]
            response["IMAGES"] = []
            if "CHUNKS" in response:
                response = add_image_to_answer(response, docs)

            self.session.checkpoint()

            rag_answer = self.build_rag_answer(response, docs, answer, show_explanation)

            self.session.checkpoint()

            return rag_answer

        #CALL_CENTER_BOT search
        if self.bot_name == BotType.CALL_CENTER_BOT.name:
            result = asyncio.run(
                    self.retrieval_augmented_generate_metadata(inquiry, max_chunks, stream, history, document_types)
                )

            response, docs = result["answer"]
            question_status = result["question_status"]
            stream_answer = result["stream_answer"]

            #da rimuovere se bisogna filtrare le immagini sui chunk di risposta
            response["IMAGES"] = []
            if "CHUNKS" in response:
                response = add_image_to_answer(response, docs)

            self.session.checkpoint()

            if len(docs) > 0: #If an answer is generated, retrieve the name of the product for which the answer is provided.
                db_client = DatabaseClient()
                product_code = docs[0].metadata["product_number"][0]
                edition = docs[0].metadata["document_edition"]
                document_used_for_answer = "\n".join([f"- {element.split("Attach")[1].split("\\")[-1]} Edition {edition}" for element in response["DOCUMENT"]])
                if product_code == "MISSING":
                    if stream_answer:
                        answer = response["ANSWER"]
                        response["APPENDIX"] = f"""The following answer is provided by the following documents:\n{document_used_for_answer}"""
                    else:
                        answer = response["ANSWER"] + f"""\n\nThe following answer is provided by the following documents:\n{document_used_for_answer}"""
                else:
                    product_name = db_client.execute(f"SELECT SHORT_DESCRIPTION FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS WHERE (INTERNAL_CODE = '{product_code}' OR PRODUCT_NUMBER = '{product_code}' OR FACTORY_MODEL = '{product_code}')", numrows=None)
                    answer = response['ANSWER']
                    response["APPENDIX"] = f"""The following answer is provided for the product {product_name[1][0]} by the following documents:\n{document_used_for_answer}"""
            else:
                answer = response["ANSWER"]
                response["APPENDIX"] = ""

            rag_answer = self.build_rag_answer(response, docs, answer, show_explanation)

            rag_answer = check_metadata(rag_answer, response)

            self.session.checkpoint()

            return rag_answer, question_status, stream_answer

    def check_missing_metadata(self, missing_metadata: dict, document_metadata: dict):
        if "product_code" in missing_metadata and document_metadata["document_number"].lower() != "missing":
            missing_metadata.remove("product_code")
        if "document_number" in missing_metadata and document_metadata["product_code"].lower() != "missing":
            missing_metadata.remove("document_number")
        if len(missing_metadata) > 0:
            answer = f"Which {", ".join(missing_metadata).replace("_", " ").replace("document number,product code", "document number or product code")} would you like to search for? {"'Product code' could be PNC, Factory Model or Internal Code" if "product_code" in missing_metadata else ""}"
        else:
            product_code = document_metadata["product_code"]
            code_type = product_code.split("*")
            if product_code != "missing" and code_type[1] == 'serial_number':
                answer = f"In order to give a more precise answer, provide the internal code or the product number to which it refers the {code_type[1]} requested"
            else:
                return ()

        docs = []
        response = {"ANSWER": answer, "DOCUMENT": docs, "EXPLANATION": 'metadata-incomplete', "CHUNKS": [], "METADATA_GATHERED": document_metadata, "MISSING_METADATA": missing_metadata }
        return (response,docs)

    def build_rag_answer(self, response: dict, docs: List[Document], answer: str, show_explanation: bool) -> RAGAnswer:

        rag_answer = RAGAnswer(data=answer)

        rag_answer.add_source_file(response["DOCUMENT"])
        if show_explanation:
            rag_answer.add_explanation(response["EXPLANATION"])
        if "APPENDIX" in response.keys():
            rag_answer.add_appendix(response["APPENDIX"])
        rag_answer.add_documents(docs)

        rag_answer.add_agent_name(self.agent_name)

        if "IMAGES" in response and len(response["IMAGES"]) > 0:
            unique_image_list = self.rag_utils._filter_duplicate_images(response)
            rag_answer.add_images(unique_image_list)

        if "METADATA_GATHERED" in response.keys():
            rag_answer.add_metadata_gathered(response["METADATA_GATHERED"])
            rag_answer.add_missing_metadata(response["MISSING_METADATA"])

        if "DOCUMENT_TYPES" in response.keys():
            rag_answer.add_document_types(response["DOCUMENT_TYPES"])
        #Used for manage status in the conversation flow.
        if response["EXPLANATION"] == "metadata-incomplete":
            rag_answer.add_status("asking_metadata")
        else:
            rag_answer.add_status("complete")

        return rag_answer

    def _get_product_name(self, response: dict, docs: List[Document]) -> dict:

        db_client = DatabaseClient()
        product_code = response["METADATA_GATHERED"]["product_code"].upper()
        edition = docs[0].metadata["document_edition"]
        document_used_for_answer = "\n".join([f"- {element.split("Attach")[1].split("\\")[-1]} Edition {edition}" for element in response["DOCUMENT"]])
        if product_code == "MISSING":
            answer = response["ANSWER"] + f"""\n\nThe following answer is provided by the following documents:\n{document_used_for_answer}"""
        else:
            product_name = db_client.execute(f"SELECT SHORT_DESCRIPTION FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS WHERE (INTERNAL_CODE = '{product_code}' OR PRODUCT_NUMBER = '{product_code}' OR FACTORY_MODEL = '{product_code}')", numrows=None)
            answer = response["ANSWER"] + f"""\n\nThe following answer is provided for the product {product_name[1][0]} by the following documents:\n{document_used_for_answer}"""

        return answer


def add_image_to_answer(bot_answer: dict, docs: List[Document]) -> dict:

    chunk_used_for_answer = bot_answer["CHUNKS"]
    if len(chunk_used_for_answer) > 0:
        for chunk in chunk_used_for_answer:
            chunk_number = int(list(chunk.keys())[0].split()[1])
            doc = docs[chunk_number-1]
            if "images" in doc.metadata:
                images = doc.metadata["images"]
                if images != []:
                    if ("image_name" in image for image in images) and type(images) != str:
                        for image in images:
                            bot_answer["IMAGES"].append(image["image_name"])
                    else:
                        bot_answer["IMAGES"].extend(ast.literal_eval(doc.metadata["images"]))

    return bot_answer

def get_args() -> Namespace:
    parser = ArgumentParser(
        description="A retrieval augmented generator that answers questions about a set of Pride documents."
    )
    parser.add_argument(
        "question",
        type=str,
        help="The natural language inquiry about the documents in the set.",
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    rag = RAG()

    response, sources = asyncio.run(
        rag.search_and_generate_answer(args.question)
    )

    for chunk in response:
        print(chunk, end="", flush=True)

    print("\n\n"+"-"*32)
    print("\nSource documents:\n")
    for i, doc in enumerate(sources):
        print(f"{i+1}. From {doc.metadata["url"]}: \"{doc.page_content}\"\n")
